{"name": "staiger", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-toast": "^1.1.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.39.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.298.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "sharp": "^0.33.1", "tailwind-merge": "^2.6.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "^5"}}