"use client";

import { useStore } from '@/store/useStore';
import { Zap, Crown, TrendingUp, Camera, Menu } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';

interface ContextBarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export function ContextBar({ sidebarOpen, setSidebarOpen }: ContextBarProps) {
  const photosUsed = useStore((state) => state.photosUsed);
  const photoLimit = useStore((state) => state.photoLimit);
  const plan = useStore((state) => state.plan);
  const user = useStore((state) => state.user);

  // Ensure we show correct values even if user isn't in database yet
  const currentPlan = user?.plan || plan || 'free';
  const currentLimit = photoLimit || 2; // Default to free plan limit
  const currentUsed = photosUsed || 0;

  const percentage = (currentUsed / currentLimit) * 100;
  const isNearLimit = percentage > 80;
  const remaining = currentLimit - currentUsed;

  const planIcons = {
    free: Camera,
    starter: TrendingUp,
    pro: Crown,
    team: Crown,
  };

  const PlanIcon = planIcons[currentPlan as keyof typeof planIcons] || Camera;

  return (
    <div className="border-b border-space-800 bg-space-900/50 px-4 md:px-6 py-3 animate-slide-in">
      <div className="flex items-center justify-between">
        {/* Mobile menu button + Plan info */}
        <div className="flex items-center space-x-4">
          {/* Mobile hamburger menu */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="lg:hidden h-8 w-8 p-0 text-space-400 hover:text-foreground"
          >
            <Menu className="h-4 w-4" />
          </Button>

          <div className="flex items-center space-x-2">
            <PlanIcon className="h-4 w-4 text-royal-400" />
            <span className="text-sm font-medium text-foreground capitalize">
              {currentPlan} Plan
            </span>
          </div>
        </div>

        {/* Photos remaining info */}
        <div className="flex items-center space-x-2 md:space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-xs md:text-sm text-space-400 hidden sm:inline">Photos remaining:</span>
            <span className="text-xs md:text-sm text-space-400 sm:hidden">Photos:</span>
            <span className={cn(
              "text-xs md:text-sm font-medium",
              isNearLimit ? "text-yellow-400" : "text-foreground"
            )}>
              {remaining}/{currentLimit}
            </span>
          </div>

          {/* Progress bar - hidden on very small screens */}
          <div 
            className="hidden sm:block w-20 md:w-32 h-2 rounded-full p-0.5 animate-gradient-flow"
            style={{
              background: isNearLimit 
                ? 'linear-gradient(45deg, #eab308, #ef4444, #f97316, #eab308, #ef4444)' 
                : 'linear-gradient(45deg, #0066ff, #9333ea, #7c3aed, #0066ff, #9333ea)',
              backgroundSize: '300% 300%'
            }}
          >
            <div className="w-full h-full bg-space-700 rounded-full overflow-hidden">
            <div
              className={cn(
                "h-full transition-all duration-700 ease-out rounded-full transform origin-left",
                isNearLimit 
                  ? "bg-gradient-to-r from-yellow-500 to-red-500" 
                  : "bg-gradient-to-r from-royal-500 to-purple-600",
                percentage > 0 && "animate-scale-in-x"
              )}
              style={{ 
                width: `${Math.min(percentage, 100)}%`,
                animationDelay: '0.3s'
              }}
                          />
            </div>
          </div>

          {isNearLimit && (
            <div className="animate-pulse-glow rounded-lg bg-yellow-500/10 px-2 py-1">
              <span className="text-xs text-yellow-400">Low</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 