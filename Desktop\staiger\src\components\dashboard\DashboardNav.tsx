"use client";

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { 
  LayoutDashboard, 
  Building2, 
  Camera, 
  Settings, 
  Crown,
  BarChart3,
  LogOut,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useStore } from '@/store/useStore';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

interface DashboardNavProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export function DashboardNav({ sidebarOpen, setSidebarOpen }: DashboardNavProps) {
  const pathname = usePathname();
  const supabase = createClientComponentClient();
  const { clearUser } = useStore();

  const navItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      description: 'Overview and analytics'
    },
    {
      name: 'Properties',
      href: '/dashboard/properties',
      icon: Building2,
      description: 'Manage your properties'
    },
    {
      name: 'Process Photos',
      href: '/dashboard/process',
      icon: Camera,
      description: 'AI virtual staging'
    },
    {
      name: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
      description: 'Account preferences'
    },
  ];

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      clearUser();
      window.location.href = '/login';
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const closeSidebar = () => setSidebarOpen(false);

  return (
    <>
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={closeSidebar}
        />
      )}

      {/* Sidebar */}
      <nav className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-space-900/95 lg:bg-space-900/50 backdrop-blur-sm border-r border-space-700 flex flex-col
        transform transition-transform duration-300 ease-in-out lg:transform-none
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Mobile close button */}
        <div className="lg:hidden absolute top-4 right-4 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={closeSidebar}
            className="h-8 w-8 p-0 text-space-400 hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Logo/Brand */}
        <div className="p-6 border-b border-space-700">
          <Link href="/dashboard" className="flex items-center space-x-3" onClick={closeSidebar}>
            <div className="h-8 w-8 bg-gradient-to-br from-royal-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Crown className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-foreground">Staiger AI</span>
          </Link>
        </div>

        {/* Navigation Items */}
        <div className="flex-1 px-4 py-6 overflow-y-auto">
          <div className="space-y-2">
            {navItems.map((item) => {
              const isActive = pathname === item.href || 
                (item.href !== '/dashboard' && pathname.startsWith(item.href));
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={closeSidebar}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group ${
                    isActive
                      ? 'bg-royal-500/20 text-royal-400 border border-royal-500/30'
                      : 'text-space-400 hover:text-foreground hover:bg-space-800/50'
                  }`}
                >
                  <item.icon className={`h-5 w-5 ${isActive ? 'text-royal-400' : 'text-space-400 group-hover:text-foreground'}`} />
                  <div className="flex-1">
                    <div className={`font-medium ${isActive ? 'text-royal-400' : 'text-space-300 group-hover:text-foreground'}`}>
                      {item.name}
                    </div>
                    {item.description && (
                      <div className="text-xs text-space-500 group-hover:text-space-400">
                        {item.description}
                      </div>
                    )}
                  </div>
                </Link>
              );
            })}
          </div>

          {/* Upgrade Section */}
          <div className="mt-8 p-4 rounded-lg bg-gradient-to-br from-royal-500/10 to-purple-600/10 border border-royal-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <Crown className="h-4 w-4 text-royal-400" />
              <span className="text-sm font-medium text-royal-400">Upgrade Plan</span>
            </div>
            <p className="text-xs text-space-400 mb-3">
              Get more photos per property and advanced features
            </p>
            <Button
              variant="premium"
              size="sm"
              className="w-full"
              onClick={() => {
                window.location.href = '/pricing';
                closeSidebar();
              }}
            >
              View Plans
            </Button>
          </div>

          {/* Quick Stats */}
          <div className="mt-6 p-4 rounded-lg bg-space-800/30 border border-space-700">
            <div className="flex items-center space-x-2 mb-3">
              <BarChart3 className="h-4 w-4 text-space-400" />
              <span className="text-sm font-medium text-space-300">Quick Stats</span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-space-400">This Month</span>
                <span className="text-foreground font-medium">0 photos</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-space-400">Properties</span>
                <span className="text-foreground font-medium">0</span>
              </div>
            </div>
          </div>
        </div>

        {/* Sign Out */}
        <div className="p-4 border-t border-space-700">
          <Button
            variant="ghost"
            onClick={handleSignOut}
            className="w-full justify-start text-space-400 hover:text-foreground hover:bg-space-800/50"
          >
            <LogOut className="h-4 w-4 mr-3" />
            Sign Out
          </Button>
        </div>
      </nav>
    </>
  );
} 