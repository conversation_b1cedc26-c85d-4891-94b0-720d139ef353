import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') ?? '/dashboard';

  if (code) {
    const supabase = createRouteHandlerClient({ cookies });
    
    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('Auth callback error:', error);
        return NextResponse.redirect(new URL('/login?error=auth_callback_error', request.url));
      }

      if (data.user) {
        // Check if user profile exists, create if not
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profileError && profileError.code === 'PGRST116') {
          // Profile doesn't exist, create it
          const { error: insertError } = await supabase
            .from('users')
            .insert({
              id: data.user.id,
              email: data.user.email,
              full_name: data.user.user_metadata?.full_name || '',
              plan: 'free',
              created_at: new Date().toISOString(),
              last_login: new Date().toISOString(),
            });

          if (insertError) {
            console.error('Error creating user profile:', insertError);
          }
        } else if (!profileError) {
          // Update last login
          await supabase
            .from('users')
            .update({ last_login: new Date().toISOString() })
            .eq('id', data.user.id);
        }
      }

      // Redirect to the intended page or dashboard
      return NextResponse.redirect(new URL(next, request.url));
    } catch (error) {
      console.error('Unexpected auth callback error:', error);
      return NextResponse.redirect(new URL('/login?error=unexpected_error', request.url));
    }
  }

  // If no code, redirect to login
  return NextResponse.redirect(new URL('/login', request.url));
} 