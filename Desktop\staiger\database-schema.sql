-- Staiger AI Database Schema
-- This should be run in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  plan TEXT NOT NULL DEFAULT 'free' CHECK (plan IN ('free', 'starter', 'pro', 'team')),
  stripe_customer_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Properties table
CREATE TABLE IF NOT EXISTS properties (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  address TEXT NOT NULL,
  property_type TEXT NOT NULL DEFAULT 'residential',
  photos_used INTEGER DEFAULT 0 NOT NULL,
  photo_limit INTEGER NOT NULL,
  property_index INTEGER NOT NULL DEFAULT 0, -- For decay calculation
  fingerprint_hash TEXT, -- For abuse prevention
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT photos_used_non_negative CHECK (photos_used >= 0),
  CONSTRAINT photo_limit_positive CHECK (photo_limit > 0),
  CONSTRAINT photos_used_within_limit CHECK (photos_used <= photo_limit)
);

-- Processing jobs table
CREATE TABLE IF NOT EXISTS processing_jobs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE NOT NULL,
  operation_type TEXT NOT NULL CHECK (operation_type IN ('declutter', 'staging', 'chain')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  input_image_url TEXT NOT NULL,
  output_image_url TEXT,
  photo_cost INTEGER NOT NULL DEFAULT 1,
  metadata JSONB DEFAULT '{}',
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage logs table for analytics and billing
CREATE TABLE IF NOT EXISTS usage_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE NOT NULL,
  job_id UUID REFERENCES processing_jobs(id) ON DELETE CASCADE NOT NULL,
  photos_consumed INTEGER NOT NULL,
  operation_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Image fingerprints for abuse prevention
CREATE TABLE IF NOT EXISTS image_fingerprints (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE NOT NULL,
  image_hash TEXT NOT NULL,
  image_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate hashes across different properties by same user
  UNIQUE(user_id, image_hash)
);

-- API keys table (for future API access)
CREATE TABLE IF NOT EXISTS api_keys (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  key_name TEXT NOT NULL,
  key_hash TEXT NOT NULL UNIQUE,
  key_prefix TEXT NOT NULL, -- First 8 chars for display
  permissions JSONB DEFAULT '{"read": true, "write": false}',
  last_used_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_properties_user_id ON properties(user_id);
CREATE INDEX IF NOT EXISTS idx_properties_created_at ON properties(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_processing_jobs_user_id ON processing_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_processing_jobs_property_id ON processing_jobs(property_id);
CREATE INDEX IF NOT EXISTS idx_processing_jobs_status ON processing_jobs(status);
CREATE INDEX IF NOT EXISTS idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_logs_created_at ON usage_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_image_fingerprints_hash ON image_fingerprints(image_hash);

-- Row Level Security (RLS) Policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE processing_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE image_fingerprints ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own properties" ON properties FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own properties" ON properties FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own properties" ON properties FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own properties" ON properties FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own jobs" ON processing_jobs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own jobs" ON processing_jobs FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own jobs" ON processing_jobs FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own usage logs" ON usage_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Service can create usage logs" ON usage_logs FOR INSERT WITH CHECK (true); -- Allow service to log

CREATE POLICY "Users can view own fingerprints" ON image_fingerprints FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own fingerprints" ON image_fingerprints FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own API keys" ON api_keys FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own API keys" ON api_keys FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own API keys" ON api_keys FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own API keys" ON api_keys FOR DELETE USING (auth.uid() = user_id);

-- Functions for automatic user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, plan)
  VALUES (NEW.id, NEW.email, 'free');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_properties_updated_at BEFORE UPDATE ON properties
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_processing_jobs_updated_at BEFORE UPDATE ON processing_jobs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate property index for new properties
CREATE OR REPLACE FUNCTION get_next_property_index(user_id_param UUID)
RETURNS INTEGER AS $$
DECLARE
  next_index INTEGER;
BEGIN
  SELECT COALESCE(MAX(property_index), -1) + 1
  INTO next_index
  FROM properties
  WHERE user_id = user_id_param;
  
  RETURN next_index;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get photo limit based on plan and property index
CREATE OR REPLACE FUNCTION get_photo_limit(plan_name TEXT, prop_index INTEGER)
RETURNS INTEGER AS $$
DECLARE
  base_limit INTEGER;
  decay_interval INTEGER;
  decay_amount INTEGER;
  minimum_limit INTEGER;
  decay_steps INTEGER;
  final_limit INTEGER;
BEGIN
  -- Set plan parameters
  CASE plan_name
    WHEN 'free' THEN
      base_limit := 2;
      decay_interval := 0; -- No decay
      decay_amount := 0;
      minimum_limit := 2;
    WHEN 'starter' THEN
      base_limit := 5;
      decay_interval := 10;
      decay_amount := 1;
      minimum_limit := 2;
    WHEN 'pro' THEN
      base_limit := 15;
      decay_interval := 20;
      decay_amount := 1;
      minimum_limit := 5;
    WHEN 'team' THEN
      base_limit := 50;
      decay_interval := 50;
      decay_amount := 1;
      minimum_limit := 10;
    ELSE
      base_limit := 2; -- Default to free
      decay_interval := 0;
      decay_amount := 0;
      minimum_limit := 2;
  END CASE;

  -- Calculate decay
  IF decay_interval > 0 THEN
    decay_steps := prop_index / decay_interval;
    final_limit := base_limit - (decay_steps * decay_amount);
    final_limit := GREATEST(final_limit, minimum_limit);
  ELSE
    final_limit := base_limit;
  END IF;

  RETURN final_limit;
END;
$$ LANGUAGE plpgsql IMMUTABLE; 