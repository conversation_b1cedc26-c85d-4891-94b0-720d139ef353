import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export const supabase = createClientComponentClient();

// Database Types
export interface User {
  id: string;
  email: string;
  full_name?: string;
  plan: 'free' | 'starter' | 'pro' | 'team';
  stripe_customer_id?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface Property {
  id: string;
  user_id: string;
  address: string;
  property_type: string;
  photos_used: number;
  photo_limit: number;
  property_index: number;
  created_at: string;
  updated_at: string;
}

export interface ProcessingJob {
  id: string;
  user_id: string;
  property_id: string;
  operation_type: 'declutter' | 'staging' | 'chain';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  input_image_url: string;
  output_image_url?: string;
  photo_cost: number;
  metadata?: any;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface UsageLog {
  id: string;
  user_id: string;
  property_id?: string;
  job_id?: string;
  photos_consumed: number;
  operation_type: string;
  metadata?: any;
  created_at: string;
}

export interface ImageFingerprint {
  id: string;
  user_id: string;
  property_id: string;
  image_hash: string;
  image_url: string;
  created_at: string;
}

// User Profile Functions
export async function getUserProfile(userId: string): Promise<User | null> {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }

  return data;
}

export async function updateUserProfile(userId: string, updates: Partial<User>): Promise<User | null> {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();

  if (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }

  return data;
}

// Property Functions
export async function getUserProperties(userId: string): Promise<Property[]> {
  const { data, error } = await supabase
    .from('properties')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching properties:', error);
    return [];
  }

  return data || [];
}

export async function createProperty(
  userId: string,
  address: string,
  propertyType: string,
  photoLimit: number,
  propertyIndex: number
): Promise<Property | null> {
  const { data, error } = await supabase
    .from('properties')
    .insert({
      user_id: userId,
      address,
      property_type: propertyType,
      photo_limit: photoLimit,
      property_index: propertyIndex,
      photos_used: 0,
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating property:', error);
    throw error;
  }

  return data;
}

export async function updatePropertyUsage(propertyId: string, photosUsed: number): Promise<Property | null> {
  const { data, error } = await supabase
    .from('properties')
    .update({ photos_used: photosUsed })
    .eq('id', propertyId)
    .select()
    .single();

  if (error) {
    console.error('Error updating property usage:', error);
    throw error;
  }

  return data;
}

// Processing Job Functions
export async function createProcessingJob(
  userId: string,
  propertyId: string,
  operationType: 'declutter' | 'staging' | 'chain',
  photoCost: number,
  inputImageUrl: string,
  metadata: any = {}
): Promise<ProcessingJob | null> {
  const { data, error } = await supabase
    .from('processing_jobs')
    .insert({
      user_id: userId,
      property_id: propertyId,
      operation_type: operationType,
      photo_cost: photoCost,
      input_image_url: inputImageUrl,
      status: 'pending',
      metadata,
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating processing job:', error);
    throw error;
  }

  return data;
}

export async function updateProcessingJob(
  jobId: string,
  updates: {
    status?: 'pending' | 'processing' | 'completed' | 'failed';
    output_image_url?: string;
    error_message?: string;
  }
): Promise<ProcessingJob | null> {
  const { data, error } = await supabase
    .from('processing_jobs')
    .update(updates)
    .eq('id', jobId)
    .select()
    .single();

  if (error) {
    console.error('Error updating processing job:', error);
    throw error;
  }

  return data;
}

export async function getUserProcessingJobs(userId: string, limit: number = 10): Promise<any[]> {
  const { data, error } = await supabase
    .from('processing_jobs')
    .select(`
      *,
      properties (
        address,
        property_type
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching processing jobs:', error);
    return [];
  }

  return data || [];
}

// Usage Analytics Functions
export async function getUserUsageStats(userId: string) {
  try {
    // Get total properties
    const { count: totalProperties } = await supabase
      .from('properties')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Get total photos processed
    const { count: totalPhotosProcessed } = await supabase
      .from('processing_jobs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('status', 'completed');

    // Get this month's usage
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { count: thisMonthUsage } = await supabase
      .from('processing_jobs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('status', 'completed')
      .gte('created_at', startOfMonth.toISOString());

    // Get recent activity
    const { data: recentActivity } = await supabase
      .from('processing_jobs')
      .select(`
        *,
        properties (
          address
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    return {
      totalProperties: totalProperties || 0,
      totalPhotosProcessed: totalPhotosProcessed || 0,
      thisMonthUsage: thisMonthUsage || 0,
      recentActivity: recentActivity || [],
    };
  } catch (error) {
    console.error('Error fetching usage stats:', error);
    return {
      totalProperties: 0,
      totalPhotosProcessed: 0,
      thisMonthUsage: 0,
      recentActivity: [],
    };
  }
}

// File Upload Functions
export async function uploadImage(file: File, bucket: string, path: string) {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false,
    });

  if (error) {
    throw error;
  }

  return data;
}

export async function getImageUrl(bucket: string, path: string): string {
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path);

  return data.publicUrl;
}

// Image Fingerprint Functions
export async function storeImageFingerprint(
  userId: string,
  propertyId: string,
  imageHash: string,
  imageUrl: string
): Promise<ImageFingerprint | null> {
  const { data, error } = await supabase
    .from('image_fingerprints')
    .insert({
      user_id: userId,
      property_id: propertyId,
      image_hash: imageHash,
      image_url: imageUrl,
    })
    .select()
    .single();

  if (error) {
    console.error('Error storing image fingerprint:', error);
    throw error;
  }

  return data;
}

export async function checkImageFingerprint(userId: string, imageHash: string): Promise<ImageFingerprint | null> {
  const { data, error } = await supabase
    .from('image_fingerprints')
    .select('*')
    .eq('user_id', userId)
    .eq('image_hash', imageHash)
    .single();

  if (error && error.code !== 'PGRST116') {
    console.error('Error checking image fingerprint:', error);
    throw error;
  }

  return data;
}

// Usage Logging Functions
export async function logUsage(
  userId: string,
  propertyId: string | null,
  jobId: string | null,
  photosConsumed: number,
  operationType: string,
  metadata: any = {}
): Promise<UsageLog | null> {
  const { data, error } = await supabase
    .from('usage_logs')
    .insert({
      user_id: userId,
      property_id: propertyId,
      job_id: jobId,
      photos_consumed: photosConsumed,
      operation_type: operationType,
      metadata,
    })
    .select()
    .single();

  if (error) {
    console.error('Error logging usage:', error);
    throw error;
  }

  return data;
}

// Job Status Update Function
export async function updateJobStatus(
  jobId: string,
  status: 'processing' | 'completed' | 'failed',
  outputImageUrl?: string
): Promise<boolean> {
  try {
    const updateData: any = { 
      status, 
      updated_at: new Date().toISOString() 
    };
    
    if (outputImageUrl) {
      updateData.output_image_url = outputImageUrl;
    }
    
    const { error } = await supabase
      .from('processing_jobs')
      .update(updateData)
      .eq('id', jobId);
    
    if (error) {
      console.error('Error updating job status:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error updating job status:', error);
    return false;
  }
}

// Dashboard Stats Functions
export async function getDashboardStats(userId: string) {
  try {
    const [usageStats, userProfile] = await Promise.all([
      getUserUsageStats(userId),
      getUserProfile(userId),
    ]);

    return {
      ...usageStats,
      userPlan: userProfile?.plan || 'free',
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return {
      totalProperties: 0,
      totalPhotosProcessed: 0,
      thisMonthUsage: 0,
      recentActivity: [],
      userPlan: 'free',
    };
  }
} 