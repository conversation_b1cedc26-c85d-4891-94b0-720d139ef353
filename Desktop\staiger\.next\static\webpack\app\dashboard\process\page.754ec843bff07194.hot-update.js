"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/process/page",{

/***/ "(app-pages-browser)/./src/hooks/useImageProcessing.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useImageProcessing.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useImageProcessing: function() { return /* binding */ useImageProcessing; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n\n\nfunction useImageProcessing() {\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [queue, setQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [currentProcessingIndex, setCurrentProcessingIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\n    const { setIsProcessing: setGlobalProcessing } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)();\n    const processImage = async (param)=>{\n        let { propertyId, operation, roomType, designStyle, image, forceReprocess } = param;\n        try {\n            setError(null);\n            // Validate image file\n            if (!image || !image.type.startsWith(\"image/\")) {\n                throw new Error(\"Please select a valid image file\");\n            }\n            // Check file size (max 10MB)\n            const maxSize = 10 * 1024 * 1024; // 10MB\n            if (image.size > maxSize) {\n                throw new Error(\"Image file size must be less than 10MB\");\n            }\n            const formData = new FormData();\n            formData.append(\"propertyId\", propertyId);\n            formData.append(\"operation\", operation);\n            formData.append(\"roomType\", roomType);\n            if (designStyle) {\n                formData.append(\"designStyle\", designStyle);\n            }\n            if (forceReprocess) {\n                formData.append(\"forceReprocess\", \"true\");\n            }\n            formData.append(\"image\", image);\n            const response = await fetch(\"/api/process\", {\n                method: \"POST\",\n                body: formData\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                // Handle duplicate detection with reprocess option\n                if (response.status === 409 && data.canReprocess) {\n                    throw new Error(\"\".concat(data.error, \"\\n\\n\").concat(data.suggestion));\n                }\n                throw new Error(data.error || \"Failed to process image\");\n            }\n            // Handle successful processing\n            if (data.success) {\n                return {\n                    success: true,\n                    jobId: data.jobId,\n                    outputImageUrl: data.outputImageUrl,\n                    photosConsumed: data.photosConsumed\n                };\n            } else {\n                throw new Error(\"Image processing failed\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"An error occurred\";\n            setError(errorMessage);\n            throw new Error(errorMessage);\n        }\n    };\n    const processQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (param)=>{\n        let { propertyId, operation, roomType, designStyle, images, forceReprocess } = param;\n        try {\n            setIsProcessing(true);\n            setGlobalProcessing(true);\n            setError(null);\n            setCurrentProcessingIndex(-1);\n            // Initialize queue\n            const initialQueue = images.map((file, index)=>({\n                    id: \"\".concat(Date.now(), \"-\").concat(index),\n                    file,\n                    status: \"pending\"\n                }));\n            setQueue(initialQueue);\n            const results = [];\n            // Process each image sequentially\n            for(let i = 0; i < images.length; i++){\n                const item = initialQueue[i];\n                setCurrentProcessingIndex(i);\n                // Update queue item status to processing\n                setQueue((prev)=>prev.map((qItem)=>qItem.id === item.id ? {\n                            ...qItem,\n                            status: \"processing\"\n                        } : qItem));\n                try {\n                    const result = await processImage({\n                        propertyId,\n                        operation,\n                        roomType,\n                        designStyle,\n                        image: item.file,\n                        forceReprocess\n                    });\n                    if (result) {\n                        results.push(result);\n                        // Update queue item status to completed\n                        setQueue((prev)=>prev.map((qItem)=>qItem.id === item.id ? {\n                                    ...qItem,\n                                    status: \"completed\",\n                                    result\n                                } : qItem));\n                    } else {\n                        throw new Error(\"Processing failed\");\n                    }\n                } catch (err) {\n                    const errorMessage = err instanceof Error ? err.message : \"Processing failed\";\n                    // Update queue item status to failed\n                    setQueue((prev)=>prev.map((qItem)=>qItem.id === item.id ? {\n                                ...qItem,\n                                status: \"failed\",\n                                error: errorMessage\n                            } : qItem));\n                    // Continue processing other images even if one fails\n                    console.error(\"Failed to process image \".concat(i + 1, \":\"), errorMessage);\n                }\n            }\n            setCurrentProcessingIndex(-1);\n            return results;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n            return [];\n        } finally{\n            setIsProcessing(false);\n            setGlobalProcessing(false);\n        }\n    }, []);\n    const clearQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setQueue([]);\n        setCurrentProcessingIndex(-1);\n    }, []);\n    const createProperty = async (address)=>{\n        try {\n            setError(null);\n            // Validate address\n            if (!address || address.trim().length < 5) {\n                throw new Error(\"Please enter a valid property address\");\n            }\n            const response = await fetch(\"/api/properties\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    address: address.trim()\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create property\");\n            }\n            return data.property;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n            return null;\n        }\n    };\n    const getProperties = async ()=>{\n        try {\n            setError(null);\n            const response = await fetch(\"/api/properties\");\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to fetch properties\");\n            }\n            return data.properties || [];\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n            return [];\n        }\n    };\n    const clearError = ()=>{\n        setError(null);\n    };\n    return {\n        processImage,\n        processQueue,\n        createProperty,\n        getProperties,\n        isProcessing,\n        error,\n        queue,\n        currentProcessingIndex,\n        clearQueue,\n        clearError\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useImageProcessing.ts\n"));

/***/ })

});