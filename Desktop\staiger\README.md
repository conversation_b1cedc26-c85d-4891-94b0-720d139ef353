# Staiger AI - Virtual Staging Platform

Transform real estate photos with AI-powered virtual staging and decluttering.

## 🏗️ Architecture Overview

Staiger AI uses a **master API key approach** similar to Cursor AI and other successful SaaS platforms:

- **Single Company Account**: We manage one set of AI API keys for all users
- **Usage Tracking**: Internal tracking per user for billing and limits
- **Better UX**: Users don't need to setup their own API keys
- **Cost Control**: Bulk pricing and centralized cost management
- **Rate Limiting**: Company-controlled rate limiting and abuse prevention

## 🚀 Features

- **AI Virtual Staging**: Transform empty rooms with realistic furniture
- **Smart Decluttering**: Remove clutter while preserving room structure  
- **Chain Operations**: Declutter + Stage in one workflow
- **Photo Limits**: Context-based pricing with decay system
- **Abuse Prevention**: Image fingerprinting and address verification
- **Real-time Processing**: Track job status and usage
- **Professional Watermarks**: "Staiger AI Virtualization" bottom-left placement

## 📋 Pricing Plans

| Plan | Price | Photos/Property | Decay | Minimum |
|------|-------|----------------|-------|---------|
| Free | $0 | 2 | None | 2 |
| Starter | $10 | 5 | After 10 properties | 2 |
| Pro | $30 | 15 | After 20 properties | 5 |
| Team | $99 | 50 | After 50 properties | 10 |

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (Auth, Database, Storage)
- **AI Processing**: Replicate API, OpenAI (master keys)
- **Payments**: Stripe
- **State Management**: Zustand
- **Animations**: Pure CSS (no Framer Motion)

## 📊 Database Schema

### Core Tables
- `users` - User profiles and plan information
- `properties` - Property details with photo limits
- `processing_jobs` - AI processing job tracking
- `usage_logs` - Detailed usage analytics
- `image_fingerprints` - Abuse prevention

## 🔧 Setup Instructions

### 1. Clone and Install

```bash
git clone <repository-url>
cd staiger
npm install
```

### 2. Database Setup

1. Create a new Supabase project
2. Run the SQL schema from `database-schema.sql` in Supabase SQL Editor
3. Enable Row Level Security (RLS) policies
4. Create storage bucket named `property-images`

### 3. Environment Configuration

```bash
cp .env.local.example .env.local
```

Fill in your environment variables:

```env
# Required - Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Required - AI Processing (Master Keys)
REPLICATE_API_TOKEN=your_replicate_token
OPENAI_API_KEY=your_openai_key

# Required - Stripe (for payments)
STRIPE_SECRET_KEY=your_stripe_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable
```

### 4. AI Model Setup

Update `src/services/ai/AIService.ts` with your actual model versions:

```typescript
// Replace these with your actual Replicate model versions
version: "your-declutter-model-version"
version: "your-staging-model-version"
```

### 5. Run Development Server

```bash
npm run dev
```

Visit `http://localhost:3000`

## 🔑 API Key Management Strategy

### Why Master API Keys?

1. **Better Economics**: Bulk pricing from AI providers
2. **Simplified UX**: No user API key setup required
3. **Usage Control**: Centralized rate limiting and monitoring
4. **Cost Predictability**: Fixed costs vs. variable user spending
5. **Better Support**: Single point of contact with AI providers

### Implementation

```typescript
// All AI requests use company keys
const response = await fetch('https://api.replicate.com/v1/predictions', {
  headers: {
    'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
  },
  // ...
});

// Track usage per user internally
await logUsage(userId, propertyId, jobId, photosConsumed, operationType);
```

### Cost Monitoring

```typescript
// Track API costs for billing
const stats = await AIService.getAPIUsageStats();
console.log(`Total API cost: $${stats.totalCost}`);
```

## 🛡️ Abuse Prevention

### Image Fingerprinting
- Perceptual hashing to detect duplicate uploads
- Prevents users from re-uploading same images

### Address Verification
- Requires real street addresses for properties
- Limits one property per address (with agency override)

### Rate Limiting
- Max 3 properties per hour per user
- Cooldown periods for excessive usage

### Usage Monitoring
- Transparent usage logs for users
- Admin dashboard for monitoring patterns

## 🚀 Deployment

### Vercel (Recommended)

```bash
npm run build
vercel deploy
```

### Environment Variables in Production

Set all environment variables in your deployment platform:
- Supabase credentials
- AI API keys (master keys)
- Stripe keys
- Other service keys

## 📈 Scaling Considerations

### API Costs
- Monitor per-user costs vs. subscription revenue
- Implement usage alerts and automatic scaling
- Negotiate enterprise rates with AI providers

### Performance
- Implement Redis for rate limiting
- Use CDN for processed images
- Queue system for batch processing

### Security
- Rotate API keys regularly
- Monitor for unusual usage patterns
- Implement IP-based rate limiting

## 🔄 Development Workflow

### Adding New AI Operations

1. Update `ProcessingOptions` interface
2. Add operation cost in `getPhotoCost()`
3. Implement processing logic in `AIService`
4. Update database schema if needed
5. Add UI components for new operation

### Testing

```bash
npm run test
npm run test:e2e
```

## 📝 API Documentation

### Process Image

```typescript
const result = await AIService.processImage(
  userId,
  propertyId,
  imageFile,
  {
    operationType: 'staging',
    roomType: 'living_room',
    designStyle: 'modern'
  }
);
```

### Check Usage

```typescript
const stats = await getUserStats(userId);
console.log(`Photos used: ${stats.photosUsed}`);
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- Documentation: [docs.staigerai.com](https://docs.staigerai.com)
- Email: <EMAIL>
- Discord: [Join our community](https://discord.gg/staigerai)

---

**Staiger AI** - Transforming real estate photography with AI
