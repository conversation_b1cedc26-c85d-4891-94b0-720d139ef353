"use client";

import { <PERSON>, CardContent, CardHeader } from './ui/Card';
import { Button } from './ui/Button';
import { Building2, MapPin, Calendar, Image as ImageIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PropertyCardProps {
  property: {
    id: string;
    address: string;
    type: string;
    rooms: number;
    images: number;
    createdAt: string;
  };
  onSelect?: (propertyId: string) => void;
  isSelected?: boolean;
  className?: string;
}

export function PropertyCard({ 
  property, 
  onSelect, 
  isSelected = false,
  className 
}: PropertyCardProps) {
  const handleSelect = () => {
    onSelect?.(property.id);
  };

  return (
    <Card 
      className={cn(
        'cursor-pointer transition-all duration-300 hover:scale-105',
        isSelected && 'ring-2 ring-royal-500 bg-royal-500/10',
        className
      )}
      onClick={handleSelect}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-royal-400" />
            <h3 className="font-medium text-foreground truncate">{property.type}</h3>
          </div>
          <div className="flex items-center space-x-1 text-xs text-space-400">
            <ImageIcon className="h-3 w-3" />
            <span>{property.images}</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-start space-x-2">
          <MapPin className="h-4 w-4 text-space-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-space-300 line-clamp-2">{property.address}</p>
        </div>
        
        <div className="flex items-center justify-between text-xs text-space-400">
          <div className="flex items-center space-x-1">
            <Calendar className="h-3 w-3" />
            <span>{new Date(property.createdAt).toLocaleDateString()}</span>
          </div>
          <span>{property.rooms} rooms</span>
        </div>
        
        <div className="pt-2">
          <Button 
            variant={isSelected ? 'premium' : 'outline'} 
            size="sm" 
            className="w-full"
            onClick={(e) => {
              e.stopPropagation();
              handleSelect();
            }}
          >
            {isSelected ? 'Selected' : 'Select Property'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 