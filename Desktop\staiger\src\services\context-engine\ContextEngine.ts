export interface PlanLimits {
  name: string;
  photosPerProperty: number;
  decayAfterProperties: number;
  decayAmount: number;
  minimumPhotos: number;
  features: string[];
}

export interface ProcessingOperation {
  type: 'declutter' | 'staging' | 'chain';
  photoCost: number;
  description: string;
  agentRole: string;
}

// Simple interfaces for the current system
interface Property {
  id: string;
  photos_used?: number;
  photo_limit?: number;
}

interface User {
  id: string;
  plan?: string;
  totalPhotosUsed?: number;
}

export class ContextEngine {
  private static readonly PLAN_CONFIGS: Record<string, PlanLimits> = {
    free: {
      name: 'Free',
      photosPerProperty: 2,
      decayAfterProperties: 0, // No decay
      decayAmount: 0,
      minimumPhotos: 2,
      features: ['Basic staging', 'Basic decluttering', 'Standard processing']
    },
    starter: {
      name: 'Starter',
      photosPerProperty: 5,
      decayAfterProperties: 10,
      decayAmount: 1,
      minimumPhotos: 2,
      features: ['Priority processing', 'Advanced staging styles', 'Email support']
    },
    pro: {
      name: 'Pro',
      photosPerProperty: 15,
      decayAfterProperties: 20,
      decayAmount: 1,
      minimumPhotos: 5,
      features: ['Premium styling options', 'Priority support', 'Bulk processing', 'Advanced decluttering']
    },
    team: {
      name: 'Team',
      photosPerProperty: 50,
      decayAfterProperties: 50,
      decayAmount: 1,
      minimumPhotos: 10,
      features: ['API access', '24/7 support', 'Custom watermarks', 'Team management', 'Analytics dashboard']
    }
  };

  // AI AGENTIC OPERATIONS - Each operation represents an AI agent with specific capabilities
  private static readonly OPERATIONS: Record<string, ProcessingOperation> = {
    declutter: {
      type: 'declutter',
      photoCost: 1,
      description: 'AI Declutter Agent: Intelligently removes clutter and unwanted items while preserving room structure',
      agentRole: 'Specialized in identifying and removing clutter while maintaining architectural integrity'
    },
    staging: {
      type: 'staging',
      photoCost: 1,
      description: 'AI Staging Agent: Adds contextually appropriate furniture and decor based on room type and style preferences',
      agentRole: 'Expert interior designer that selects and places furniture to maximize appeal'
    },
    chain: {
      type: 'chain',
      photoCost: 2,
      description: 'AI Chain Agent: Complete transformation using declutter agent first, then staging agent',
      agentRole: 'Master coordinator that orchestrates multiple AI agents for complete room transformation'
    }
  };

  static getPhotoLimit(plan: string, propertyIndex: number): number {
    const config = this.PLAN_CONFIGS[plan.toLowerCase()];
    if (!config) return this.PLAN_CONFIGS.free.photosPerProperty;

    if (config.decayAfterProperties === 0) {
      return config.photosPerProperty;
    }

    const decaySteps = Math.floor(propertyIndex / config.decayAfterProperties);
    const currentLimit = config.photosPerProperty - (decaySteps * config.decayAmount);
    
    return Math.max(currentLimit, config.minimumPhotos);
  }

  static getOperationCost(operationType: string): number {
    const operation = this.OPERATIONS[operationType.toLowerCase()];
    return operation ? operation.photoCost : 1;
  }

  static getOperationDetails(operationType: string): ProcessingOperation | null {
    return this.OPERATIONS[operationType.toLowerCase()] || null;
  }

  static canProcessPhoto(
    plan: string,
    propertyIndex: number,
    currentUsage: number,
    operationType: string
  ): { canProcess: boolean; reason?: string; photosRemaining: number } {
    const photoLimit = this.getPhotoLimit(plan, propertyIndex);
    const operationCost = this.getOperationCost(operationType);
    const photosRemaining = photoLimit - currentUsage;

    if (photosRemaining < operationCost) {
      return {
        canProcess: false,
        reason: `Not enough photos remaining. Need ${operationCost}, have ${photosRemaining}`,
        photosRemaining
      };
    }

    return {
      canProcess: true,
      photosRemaining: photosRemaining - operationCost
    };
  }

  static getPlanFeatures(plan: string): string[] {
    const config = this.PLAN_CONFIGS[plan.toLowerCase()];
    return config ? config.features : this.PLAN_CONFIGS.free.features;
  }

  static getAllPlans(): PlanLimits[] {
    return Object.values(this.PLAN_CONFIGS);
  }

  static getRecommendedPlan(averagePhotosPerProperty: number): string {
    if (averagePhotosPerProperty <= 2) return 'free';
    if (averagePhotosPerProperty <= 5) return 'starter';
    if (averagePhotosPerProperty <= 15) return 'pro';
    return 'team';
  }

  static calculateMonthlyUsage(
    plan: string,
    propertiesPerMonth: number,
    photosPerProperty: number
  ): {
    totalPhotosNeeded: number;
    planCanHandle: boolean;
    suggestedPlan?: string;
  } {
    const totalPhotosNeeded = propertiesPerMonth * photosPerProperty;
    const averageLimit = this.getPhotoLimit(plan, Math.floor(propertiesPerMonth / 2));
    const planCanHandle = photosPerProperty <= averageLimit;

    return {
      totalPhotosNeeded,
      planCanHandle,
      suggestedPlan: planCanHandle ? undefined : this.getRecommendedPlan(photosPerProperty)
    };
  }

  // FIXED: Simple method for current system
  public static async canPerformOperation(
    operationType: string,
    property: any,
    user: any
  ): Promise<{ allowed: boolean; reason?: string }> {
    const cost = this.getOperationCost(operationType);
    
    // Simple check - just return allowed for now since we have basic validation in API
    return { allowed: true };
  }

  // ENHANCED: Get AI agent prompt context with spatial intelligence
  public static getAgentPromptContext(
    operationType: string,
    roomType?: string,
    designStyle?: string
  ): string {
    const operation = this.getOperationDetails(operationType);
    if (!operation) return '';

    // Build comprehensive spatial context
    let context = this.buildSpatialIntelligencePrompt(operationType, roomType, designStyle);

    return context;
  }

  // NEW: Build comprehensive spatial intelligence prompt
  private static buildSpatialIntelligencePrompt(
    operationType: string,
    roomType?: string,
    designStyle?: string
  ): string {
    const operation = this.getOperationDetails(operationType);
    if (!operation) return '';

    let prompt = `You are an expert ${operation.agentRole} with advanced spatial intelligence and architectural awareness.\n\n`;

    // Add spatial analysis instructions
    prompt += this.getSpatialAnalysisInstructions();

    // Add room-specific spatial context
    if (roomType) {
      prompt += this.getRoomSpecificSpatialContext(roomType);
    }

    // Add operation-specific instructions
    prompt += this.getOperationSpecificInstructions(operationType, designStyle);

    // Add critical spatial constraints
    prompt += this.getSpatialConstraints();

    return prompt;
  }

  // NEW: Core spatial analysis instructions
  private static getSpatialAnalysisInstructions(): string {
    return `
SPATIAL ANALYSIS PROTOCOL:
1. ANALYZE the room's architectural features:
   - Identify all doors, windows, and their swing directions
   - Locate built-in elements (cabinets, counters, fixtures)
   - Assess ceiling height and any structural elements
   - Note electrical outlets, switches, and lighting fixtures
   - Identify traffic flow patterns and circulation paths

2. MEASURE spatial relationships:
   - Estimate room dimensions and proportions
   - Calculate available floor space for furniture
   - Identify focal points and natural gathering areas
   - Assess sight lines and visual connections

3. UNDERSTAND functional zones:
   - Primary activity areas (seating, sleeping, working)
   - Secondary support areas (storage, circulation)
   - Service areas (access to utilities, maintenance)

`;
  }

  // NEW: Room-specific spatial context
  private static getRoomSpecificSpatialContext(roomType: string): string {
    const roomContexts: Record<string, string> = {
      living_room: `
LIVING ROOM SPATIAL REQUIREMENTS:
- Maintain 3-4 feet of circulation space around seating areas
- Position seating to face each other for conversation (8-10 feet apart maximum)
- Keep pathways clear from main entrance to other rooms
- Place coffee table 14-18 inches from sofa front edge
- Ensure TV viewing distance is 1.5-2.5x the screen diagonal
- Leave 6-8 inches between furniture and walls for cleaning
- Create visual balance with furniture placement and scale

`,
      bedroom: `
BEDROOM SPATIAL REQUIREMENTS:
- Maintain 2-3 feet of clearance around bed for making and accessing
- Position bed away from direct door alignment for privacy
- Ensure adequate space for closet and drawer access (3+ feet)
- Place nightstands within arm's reach of bed occupants
- Keep pathways to bathroom/closet unobstructed
- Consider window placement for natural light and ventilation
- Scale furniture appropriately to room size (avoid oversized pieces)

`,
      kitchen: `
KITCHEN SPATIAL REQUIREMENTS:
- Maintain work triangle efficiency (sink, stove, refrigerator)
- Ensure 42-48 inches of clearance for walkways and appliance access
- Keep countertop work areas clear and functional
- Position islands with adequate circulation space (36+ inches)
- Maintain clear sight lines for safety while cooking
- Consider cabinet door and drawer swing clearances
- Ensure proper ventilation and lighting over work areas

`,
      dining_room: `
DINING ROOM SPATIAL REQUIREMENTS:
- Allow 24-30 inches per person at dining table
- Maintain 36-42 inches between table edge and walls/furniture for chair access
- Position table to allow natural conversation flow
- Ensure adequate lighting over dining surface
- Keep serving areas accessible from kitchen
- Consider buffet/sideboard placement for functionality
- Scale chandelier/lighting to table size (1/2 to 2/3 table width)

`,
      bathroom: `
BATHROOM SPATIAL REQUIREMENTS:
- Maintain required clearances around fixtures (15" from centerline for toilets)
- Ensure door swing doesn't interfere with fixture use
- Keep towel storage within reach of shower/tub
- Position mirrors for optimal lighting and use
- Maintain clear floor space for safety (especially near wet areas)
- Consider accessibility and ease of cleaning
- Ensure adequate ventilation and moisture control

`,
      office: `
OFFICE SPATIAL REQUIREMENTS:
- Position desk to minimize glare on computer screen
- Maintain 3+ feet behind desk chair for movement
- Ensure adequate lighting for task work (avoid shadows)
- Keep frequently used items within arm's reach
- Position storage for easy access without disrupting workflow
- Consider cable management and electrical access
- Create distinct zones for different work activities

`
    };

    return roomContexts[roomType.toLowerCase()] || `
GENERAL ROOM SPATIAL REQUIREMENTS:
- Maintain appropriate circulation and clearance spaces
- Consider the room's primary function and user needs
- Ensure furniture scale is appropriate to room size
- Keep pathways clear and logical
- Position elements for optimal functionality and safety

`;
  }

  // NEW: Operation-specific spatial instructions
  private static getOperationSpecificInstructions(operationType: string, designStyle?: string): string {
    let instructions = '';

    switch (operationType) {
      case 'declutter':
        instructions = `
DECLUTTER SPATIAL OBJECTIVES:
- Remove ALL furniture, personal items, and clutter while preserving architectural integrity
- Maintain original room proportions and spatial relationships
- Preserve built-in elements, fixtures, and permanent installations
- Keep structural elements (walls, columns, beams) intact
- Maintain original flooring, ceiling, and wall finishes
- Preserve window and door openings and their trim
- Result should be a clean, empty space that showcases the room's bones

`;
        break;

      case 'staging':
        instructions = `
STAGING SPATIAL OBJECTIVES:
- Add furniture and decor that enhances the room's spatial qualities
- Create inviting, functional arrangements that showcase the space's potential
- Use appropriately scaled furniture that doesn't overwhelm the room
- Establish clear circulation paths and functional zones
- Highlight architectural features and natural light
- Create visual interest while maintaining spatial harmony
- Choose pieces that appeal to the broadest range of potential buyers/renters

FURNITURE PLACEMENT PRINCIPLES:
- Float furniture away from walls when space allows (creates sense of spaciousness)
- Create conversation areas with seating facing each other
- Use rugs to define spaces and add warmth
- Add vertical elements (tall plants, floor lamps) to draw the eye up
- Balance visual weight throughout the room
- Layer lighting (ambient, task, accent) for depth and warmth

`;
        break;

      case 'chain':
        instructions = `
CHAIN OPERATION SPATIAL OBJECTIVES:
- First phase: Complete decluttering following spatial preservation principles
- Second phase: Strategic staging that maximizes the revealed spatial potential
- Ensure seamless transition between phases maintains spatial integrity
- Final result should showcase dramatic transformation while respecting architecture

`;
        break;
    }

    if (designStyle && operationType !== 'declutter') {
      instructions += this.getDesignStyleSpatialGuidance(designStyle);
    }

    return instructions;
  }

  // NEW: Design style spatial guidance
  private static getDesignStyleSpatialGuidance(designStyle: string): string {
    const styleGuidance: Record<string, string> = {
      modern: `
MODERN STYLE SPATIAL APPROACH:
- Emphasize clean lines and geometric arrangements
- Use minimal, well-proportioned furniture with sleek profiles
- Create open, uncluttered spaces with strategic negative space
- Choose furniture with legs to maintain visual lightness
- Use neutral colors to enhance spatial perception
- Incorporate built-in storage solutions when possible

`,
      traditional: `
TRADITIONAL STYLE SPATIAL APPROACH:
- Create cozy, intimate seating arrangements
- Use substantial furniture pieces appropriate to room scale
- Layer textiles and accessories for warmth and comfort
- Establish formal arrangements with symmetrical balance
- Include classic proportions and time-tested layouts
- Add architectural details that enhance traditional character

`,
      contemporary: `
CONTEMPORARY STYLE SPATIAL APPROACH:
- Blend comfort with clean, updated aesthetics
- Use furniture with interesting shapes while maintaining functionality
- Create flexible spaces that can adapt to different uses
- Incorporate current trends while respecting spatial principles
- Balance bold elements with neutral backgrounds
- Emphasize natural light and connection to outdoors

`,
      minimalist: `
MINIMALIST STYLE SPATIAL APPROACH:
- Use only essential furniture pieces with perfect proportions
- Maximize negative space to create sense of calm and openness
- Choose multi-functional pieces to reduce visual clutter
- Emphasize quality over quantity in all selections
- Use monochromatic or very limited color palettes
- Let architectural features be the primary visual interest

`,
      industrial: `
INDUSTRIAL STYLE SPATIAL APPROACH:
- Expose and celebrate structural elements (beams, pipes, brick)
- Use furniture with metal and wood combinations
- Create open, loft-like arrangements with high ceilings
- Incorporate vintage or repurposed pieces with character
- Use raw materials and honest construction methods
- Maintain utilitarian functionality in all design choices

`
    };

    return styleGuidance[designStyle.toLowerCase()] || '';
  }

  // NEW: Critical spatial constraints and safety guidelines
  private static getSpatialConstraints(): string {
    return `
CRITICAL SPATIAL CONSTRAINTS - NEVER VIOLATE:
❌ DO NOT place furniture blocking doorways or natural traffic paths
❌ DO NOT position items where they would interfere with door/window operation
❌ DO NOT place furniture too close to heat sources or electrical panels
❌ DO NOT block access to built-in storage or utility areas
❌ DO NOT create arrangements that feel cramped or claustrophobic
❌ DO NOT use furniture that is dramatically out of scale with the room
❌ DO NOT place heavy items where they appear unstable or unsafe
❌ DO NOT block natural light sources unnecessarily
❌ DO NOT create dead-end spaces or awkward circulation patterns
❌ DO NOT ignore the room's architectural hierarchy and focal points

✅ ALWAYS ensure furniture appears stable and properly supported
✅ ALWAYS maintain logical traffic flow and circulation
✅ ALWAYS respect the room's proportions and scale
✅ ALWAYS consider real-world functionality and daily use
✅ ALWAYS enhance rather than fight the existing architecture
✅ ALWAYS create inviting, livable spaces that feel authentic
✅ ALWAYS use appropriate lighting to enhance spatial perception
✅ ALWAYS consider safety and accessibility in all arrangements

FINAL VERIFICATION:
Before completing the image, mentally walk through the space and verify:
- Can people move naturally through the room?
- Does the furniture arrangement support the room's intended function?
- Are all pieces appropriately scaled and positioned?
- Does the overall composition feel balanced and harmonious?
- Would this space be appealing and functional for real occupants?
`;
  }
} 