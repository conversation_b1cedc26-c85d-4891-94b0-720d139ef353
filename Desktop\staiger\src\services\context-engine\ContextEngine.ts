export interface PlanLimits {
  name: string;
  photosPerProperty: number;
  decayAfterProperties: number;
  decayAmount: number;
  minimumPhotos: number;
  features: string[];
}

export interface ProcessingOperation {
  type: 'declutter' | 'staging' | 'chain';
  photoCost: number;
  description: string;
  agentRole: string;
}

// Simple interfaces for the current system
interface Property {
  id: string;
  photos_used?: number;
  photo_limit?: number;
}

interface User {
  id: string;
  plan?: string;
  totalPhotosUsed?: number;
}

export class ContextEngine {
  private static readonly PLAN_CONFIGS: Record<string, PlanLimits> = {
    free: {
      name: 'Free',
      photosPerProperty: 2,
      decayAfterProperties: 0, // No decay
      decayAmount: 0,
      minimumPhotos: 2,
      features: ['Basic staging', 'Basic decluttering', 'Standard processing']
    },
    starter: {
      name: 'Starter',
      photosPerProperty: 5,
      decayAfterProperties: 10,
      decayAmount: 1,
      minimumPhotos: 2,
      features: ['Priority processing', 'Advanced staging styles', 'Email support']
    },
    pro: {
      name: 'Pro',
      photosPerProperty: 15,
      decayAfterProperties: 20,
      decayAmount: 1,
      minimumPhotos: 5,
      features: ['Premium styling options', 'Priority support', 'Bulk processing', 'Advanced decluttering']
    },
    team: {
      name: 'Team',
      photosPerProperty: 50,
      decayAfterProperties: 50,
      decayAmount: 1,
      minimumPhotos: 10,
      features: ['API access', '24/7 support', 'Custom watermarks', 'Team management', 'Analytics dashboard']
    }
  };

  // AI AGENTIC OPERATIONS - Each operation represents an AI agent with specific capabilities
  private static readonly OPERATIONS: Record<string, ProcessingOperation> = {
    declutter: {
      type: 'declutter',
      photoCost: 1,
      description: 'AI Declutter Agent: Intelligently removes clutter and unwanted items while preserving room structure',
      agentRole: 'Specialized in identifying and removing clutter while maintaining architectural integrity'
    },
    staging: {
      type: 'staging',
      photoCost: 1,
      description: 'AI Staging Agent: Adds contextually appropriate furniture and decor based on room type and style preferences',
      agentRole: 'Expert interior designer that selects and places furniture to maximize appeal'
    },
    chain: {
      type: 'chain',
      photoCost: 2,
      description: 'AI Chain Agent: Complete transformation using declutter agent first, then staging agent',
      agentRole: 'Master coordinator that orchestrates multiple AI agents for complete room transformation'
    }
  };

  static getPhotoLimit(plan: string, propertyIndex: number): number {
    const config = this.PLAN_CONFIGS[plan.toLowerCase()];
    if (!config) return this.PLAN_CONFIGS.free.photosPerProperty;

    if (config.decayAfterProperties === 0) {
      return config.photosPerProperty;
    }

    const decaySteps = Math.floor(propertyIndex / config.decayAfterProperties);
    const currentLimit = config.photosPerProperty - (decaySteps * config.decayAmount);
    
    return Math.max(currentLimit, config.minimumPhotos);
  }

  static getOperationCost(operationType: string): number {
    const operation = this.OPERATIONS[operationType.toLowerCase()];
    return operation ? operation.photoCost : 1;
  }

  static getOperationDetails(operationType: string): ProcessingOperation | null {
    return this.OPERATIONS[operationType.toLowerCase()] || null;
  }

  static canProcessPhoto(
    plan: string,
    propertyIndex: number,
    currentUsage: number,
    operationType: string
  ): { canProcess: boolean; reason?: string; photosRemaining: number } {
    const photoLimit = this.getPhotoLimit(plan, propertyIndex);
    const operationCost = this.getOperationCost(operationType);
    const photosRemaining = photoLimit - currentUsage;

    if (photosRemaining < operationCost) {
      return {
        canProcess: false,
        reason: `Not enough photos remaining. Need ${operationCost}, have ${photosRemaining}`,
        photosRemaining
      };
    }

    return {
      canProcess: true,
      photosRemaining: photosRemaining - operationCost
    };
  }

  static getPlanFeatures(plan: string): string[] {
    const config = this.PLAN_CONFIGS[plan.toLowerCase()];
    return config ? config.features : this.PLAN_CONFIGS.free.features;
  }

  static getAllPlans(): PlanLimits[] {
    return Object.values(this.PLAN_CONFIGS);
  }

  static getRecommendedPlan(averagePhotosPerProperty: number): string {
    if (averagePhotosPerProperty <= 2) return 'free';
    if (averagePhotosPerProperty <= 5) return 'starter';
    if (averagePhotosPerProperty <= 15) return 'pro';
    return 'team';
  }

  static calculateMonthlyUsage(
    plan: string,
    propertiesPerMonth: number,
    photosPerProperty: number
  ): {
    totalPhotosNeeded: number;
    planCanHandle: boolean;
    suggestedPlan?: string;
  } {
    const totalPhotosNeeded = propertiesPerMonth * photosPerProperty;
    const averageLimit = this.getPhotoLimit(plan, Math.floor(propertiesPerMonth / 2));
    const planCanHandle = photosPerProperty <= averageLimit;

    return {
      totalPhotosNeeded,
      planCanHandle,
      suggestedPlan: planCanHandle ? undefined : this.getRecommendedPlan(photosPerProperty)
    };
  }

  // FIXED: Simple method for current system
  public static async canPerformOperation(
    operationType: string,
    property: any,
    user: any
  ): Promise<{ allowed: boolean; reason?: string }> {
    const cost = this.getOperationCost(operationType);
    
    // Simple check - just return allowed for now since we have basic validation in API
    return { allowed: true };
  }

  // NEW: Get AI agent prompt context for Gemini 2.0 Flash
  public static getAgentPromptContext(
    operationType: string,
    roomType?: string,
    designStyle?: string
  ): string {
    const operation = this.getOperationDetails(operationType);
    if (!operation) return '';

    let context = `You are an ${operation.agentRole}. `;
    
    if (roomType) {
      context += `You are working on a ${roomType.replace('_', ' ')}. `;
    }
    
    if (designStyle && operationType !== 'declutter') {
      context += `The desired design style is ${designStyle}. `;
    }
    
    context += operation.description;
    
    return context;
  }
} 