"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/page",{

/***/ "(app-pages-browser)/./src/services/context-engine/ContextEngine.ts":
/*!******************************************************!*\
  !*** ./src/services/context-engine/ContextEngine.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextEngine: function() { return /* binding */ ContextEngine; }\n/* harmony export */ });\nclass ContextEngine {\n    static getPhotoLimit(plan, propertyIndex) {\n        const config = this.PLAN_CONFIGS[plan.toLowerCase()];\n        if (!config) return this.PLAN_CONFIGS.free.photosPerProperty;\n        if (config.decayAfterProperties === 0) {\n            return config.photosPerProperty;\n        }\n        const decaySteps = Math.floor(propertyIndex / config.decayAfterProperties);\n        const currentLimit = config.photosPerProperty - decaySteps * config.decayAmount;\n        return Math.max(currentLimit, config.minimumPhotos);\n    }\n    static getOperationCost(operationType) {\n        const operation = this.OPERATIONS[operationType.toLowerCase()];\n        return operation ? operation.photoCost : 1;\n    }\n    static getOperationDetails(operationType) {\n        return this.OPERATIONS[operationType.toLowerCase()] || null;\n    }\n    static canProcessPhoto(plan, propertyIndex, currentUsage, operationType) {\n        const photoLimit = this.getPhotoLimit(plan, propertyIndex);\n        const operationCost = this.getOperationCost(operationType);\n        const photosRemaining = photoLimit - currentUsage;\n        if (photosRemaining < operationCost) {\n            return {\n                canProcess: false,\n                reason: \"Not enough photos remaining. Need \".concat(operationCost, \", have \").concat(photosRemaining),\n                photosRemaining\n            };\n        }\n        return {\n            canProcess: true,\n            photosRemaining: photosRemaining - operationCost\n        };\n    }\n    static getPlanFeatures(plan) {\n        const config = this.PLAN_CONFIGS[plan.toLowerCase()];\n        return config ? config.features : this.PLAN_CONFIGS.free.features;\n    }\n    static getAllPlans() {\n        return Object.values(this.PLAN_CONFIGS);\n    }\n    static getRecommendedPlan(averagePhotosPerProperty) {\n        if (averagePhotosPerProperty <= 2) return \"free\";\n        if (averagePhotosPerProperty <= 5) return \"starter\";\n        if (averagePhotosPerProperty <= 15) return \"pro\";\n        return \"team\";\n    }\n    static calculateMonthlyUsage(plan, propertiesPerMonth, photosPerProperty) {\n        const totalPhotosNeeded = propertiesPerMonth * photosPerProperty;\n        const averageLimit = this.getPhotoLimit(plan, Math.floor(propertiesPerMonth / 2));\n        const planCanHandle = photosPerProperty <= averageLimit;\n        return {\n            totalPhotosNeeded,\n            planCanHandle,\n            suggestedPlan: planCanHandle ? undefined : this.getRecommendedPlan(photosPerProperty)\n        };\n    }\n    // FIXED: Simple method for current system\n    static async canPerformOperation(operationType, property, user) {\n        const cost = this.getOperationCost(operationType);\n        // Simple check - just return allowed for now since we have basic validation in API\n        return {\n            allowed: true\n        };\n    }\n    // ENHANCED: Get AI agent prompt context with spatial intelligence\n    static getAgentPromptContext(operationType, roomType, designStyle) {\n        const operation = this.getOperationDetails(operationType);\n        if (!operation) return \"\";\n        // Build comprehensive spatial context\n        let context = this.buildSpatialIntelligencePrompt(operationType, roomType, designStyle);\n        return context;\n    }\n    // NEW: Build comprehensive spatial intelligence prompt\n    static buildSpatialIntelligencePrompt(operationType, roomType, designStyle) {\n        const operation = this.getOperationDetails(operationType);\n        if (!operation) return \"\";\n        let prompt = \"You are an expert \".concat(operation.agentRole, \" with advanced spatial intelligence and architectural awareness.\\n\\n\");\n        // Add spatial analysis instructions\n        prompt += this.getSpatialAnalysisInstructions();\n        // Add room-specific spatial context\n        if (roomType) {\n            prompt += this.getRoomSpecificSpatialContext(roomType);\n        }\n        // Add operation-specific instructions\n        prompt += this.getOperationSpecificInstructions(operationType, designStyle);\n        // Add critical spatial constraints\n        prompt += this.getSpatialConstraints();\n        return prompt;\n    }\n    // NEW: Core spatial analysis instructions\n    static getSpatialAnalysisInstructions() {\n        return \"\\nSPATIAL ANALYSIS PROTOCOL:\\n1. ANALYZE the room's architectural features:\\n   - Identify all doors, windows, and their swing directions\\n   - Locate built-in elements (cabinets, counters, fixtures)\\n   - Assess ceiling height and any structural elements\\n   - Note electrical outlets, switches, and lighting fixtures\\n   - Identify traffic flow patterns and circulation paths\\n\\n2. MEASURE spatial relationships:\\n   - Estimate room dimensions and proportions\\n   - Calculate available floor space for furniture\\n   - Identify focal points and natural gathering areas\\n   - Assess sight lines and visual connections\\n\\n3. UNDERSTAND functional zones:\\n   - Primary activity areas (seating, sleeping, working)\\n   - Secondary support areas (storage, circulation)\\n   - Service areas (access to utilities, maintenance)\\n\\n\";\n    }\n    // NEW: Room-specific spatial context\n    static getRoomSpecificSpatialContext(roomType) {\n        const roomContexts = {\n            living_room: \"\\nLIVING ROOM SPATIAL REQUIREMENTS:\\n- Maintain 3-4 feet of circulation space around seating areas\\n- Position seating to face each other for conversation (8-10 feet apart maximum)\\n- Keep pathways clear from main entrance to other rooms\\n- Place coffee table 14-18 inches from sofa front edge\\n- Ensure TV viewing distance is 1.5-2.5x the screen diagonal\\n- Leave 6-8 inches between furniture and walls for cleaning\\n- Create visual balance with furniture placement and scale\\n\\n\",\n            bedroom: \"\\nBEDROOM SPATIAL REQUIREMENTS:\\n- Maintain 2-3 feet of clearance around bed for making and accessing\\n- Position bed away from direct door alignment for privacy\\n- Ensure adequate space for closet and drawer access (3+ feet)\\n- Place nightstands within arm's reach of bed occupants\\n- Keep pathways to bathroom/closet unobstructed\\n- Consider window placement for natural light and ventilation\\n- Scale furniture appropriately to room size (avoid oversized pieces)\\n\\n\",\n            kitchen: \"\\nKITCHEN SPATIAL REQUIREMENTS:\\n- Maintain work triangle efficiency (sink, stove, refrigerator)\\n- Ensure 42-48 inches of clearance for walkways and appliance access\\n- Keep countertop work areas clear and functional\\n- Position islands with adequate circulation space (36+ inches)\\n- Maintain clear sight lines for safety while cooking\\n- Consider cabinet door and drawer swing clearances\\n- Ensure proper ventilation and lighting over work areas\\n\\n\",\n            dining_room: \"\\nDINING ROOM SPATIAL REQUIREMENTS:\\n- Allow 24-30 inches per person at dining table\\n- Maintain 36-42 inches between table edge and walls/furniture for chair access\\n- Position table to allow natural conversation flow\\n- Ensure adequate lighting over dining surface\\n- Keep serving areas accessible from kitchen\\n- Consider buffet/sideboard placement for functionality\\n- Scale chandelier/lighting to table size (1/2 to 2/3 table width)\\n\\n\",\n            bathroom: \"\\nBATHROOM SPATIAL REQUIREMENTS:\\n- Maintain required clearances around fixtures (15\\\" from centerline for toilets)\\n- Ensure door swing doesn't interfere with fixture use\\n- Keep towel storage within reach of shower/tub\\n- Position mirrors for optimal lighting and use\\n- Maintain clear floor space for safety (especially near wet areas)\\n- Consider accessibility and ease of cleaning\\n- Ensure adequate ventilation and moisture control\\n\\n\",\n            office: \"\\nOFFICE SPATIAL REQUIREMENTS:\\n- Position desk to minimize glare on computer screen\\n- Maintain 3+ feet behind desk chair for movement\\n- Ensure adequate lighting for task work (avoid shadows)\\n- Keep frequently used items within arm's reach\\n- Position storage for easy access without disrupting workflow\\n- Consider cable management and electrical access\\n- Create distinct zones for different work activities\\n\\n\"\n        };\n        return roomContexts[roomType.toLowerCase()] || \"\\nGENERAL ROOM SPATIAL REQUIREMENTS:\\n- Maintain appropriate circulation and clearance spaces\\n- Consider the room's primary function and user needs\\n- Ensure furniture scale is appropriate to room size\\n- Keep pathways clear and logical\\n- Position elements for optimal functionality and safety\\n\\n\";\n    }\n    // NEW: Operation-specific spatial instructions\n    static getOperationSpecificInstructions(operationType, designStyle) {\n        let instructions = \"\";\n        switch(operationType){\n            case \"declutter\":\n                instructions = \"\\nDECLUTTER SPATIAL OBJECTIVES:\\n- Remove ALL furniture, personal items, and clutter while preserving architectural integrity\\n- Maintain original room proportions and spatial relationships\\n- Preserve built-in elements, fixtures, and permanent installations\\n- Keep structural elements (walls, columns, beams) intact\\n- Maintain original flooring, ceiling, and wall finishes\\n- Preserve window and door openings and their trim\\n- Result should be a clean, empty space that showcases the room's bones\\n\\n\";\n                break;\n            case \"staging\":\n                instructions = \"\\nSTAGING SPATIAL OBJECTIVES:\\n- Add furniture and decor that enhances the room's spatial qualities\\n- Create inviting, functional arrangements that showcase the space's potential\\n- Use appropriately scaled furniture that doesn't overwhelm the room\\n- Establish clear circulation paths and functional zones\\n- Highlight architectural features and natural light\\n- Create visual interest while maintaining spatial harmony\\n- Choose pieces that appeal to the broadest range of potential buyers/renters\\n\\nFURNITURE PLACEMENT PRINCIPLES:\\n- Float furniture away from walls when space allows (creates sense of spaciousness)\\n- Create conversation areas with seating facing each other\\n- Use rugs to define spaces and add warmth\\n- Add vertical elements (tall plants, floor lamps) to draw the eye up\\n- Balance visual weight throughout the room\\n- Layer lighting (ambient, task, accent) for depth and warmth\\n\\n\";\n                break;\n            case \"chain\":\n                instructions = \"\\nCHAIN OPERATION SPATIAL OBJECTIVES:\\n- First phase: Complete decluttering following spatial preservation principles\\n- Second phase: Strategic staging that maximizes the revealed spatial potential\\n- Ensure seamless transition between phases maintains spatial integrity\\n- Final result should showcase dramatic transformation while respecting architecture\\n\\n\";\n                break;\n        }\n        if (designStyle && operationType !== \"declutter\") {\n            instructions += this.getDesignStyleSpatialGuidance(designStyle);\n        }\n        return instructions;\n    }\n    // NEW: Design style spatial guidance\n    static getDesignStyleSpatialGuidance(designStyle) {\n        const styleGuidance = {\n            modern: \"\\nMODERN STYLE SPATIAL APPROACH:\\n- Emphasize clean lines and geometric arrangements\\n- Use minimal, well-proportioned furniture with sleek profiles\\n- Create open, uncluttered spaces with strategic negative space\\n- Choose furniture with legs to maintain visual lightness\\n- Use neutral colors to enhance spatial perception\\n- Incorporate built-in storage solutions when possible\\n\\n\",\n            traditional: \"\\nTRADITIONAL STYLE SPATIAL APPROACH:\\n- Create cozy, intimate seating arrangements\\n- Use substantial furniture pieces appropriate to room scale\\n- Layer textiles and accessories for warmth and comfort\\n- Establish formal arrangements with symmetrical balance\\n- Include classic proportions and time-tested layouts\\n- Add architectural details that enhance traditional character\\n\\n\",\n            contemporary: \"\\nCONTEMPORARY STYLE SPATIAL APPROACH:\\n- Blend comfort with clean, updated aesthetics\\n- Use furniture with interesting shapes while maintaining functionality\\n- Create flexible spaces that can adapt to different uses\\n- Incorporate current trends while respecting spatial principles\\n- Balance bold elements with neutral backgrounds\\n- Emphasize natural light and connection to outdoors\\n\\n\",\n            minimalist: \"\\nMINIMALIST STYLE SPATIAL APPROACH:\\n- Use only essential furniture pieces with perfect proportions\\n- Maximize negative space to create sense of calm and openness\\n- Choose multi-functional pieces to reduce visual clutter\\n- Emphasize quality over quantity in all selections\\n- Use monochromatic or very limited color palettes\\n- Let architectural features be the primary visual interest\\n\\n\",\n            industrial: \"\\nINDUSTRIAL STYLE SPATIAL APPROACH:\\n- Expose and celebrate structural elements (beams, pipes, brick)\\n- Use furniture with metal and wood combinations\\n- Create open, loft-like arrangements with high ceilings\\n- Incorporate vintage or repurposed pieces with character\\n- Use raw materials and honest construction methods\\n- Maintain utilitarian functionality in all design choices\\n\\n\"\n        };\n        return styleGuidance[designStyle.toLowerCase()] || \"\";\n    }\n    // NEW: Critical spatial constraints and safety guidelines\n    static getSpatialConstraints() {\n        return \"\\nCRITICAL SPATIAL CONSTRAINTS - NEVER VIOLATE:\\n❌ DO NOT place furniture blocking doorways or natural traffic paths\\n❌ DO NOT position items where they would interfere with door/window operation\\n❌ DO NOT place furniture too close to heat sources or electrical panels\\n❌ DO NOT block access to built-in storage or utility areas\\n❌ DO NOT create arrangements that feel cramped or claustrophobic\\n❌ DO NOT use furniture that is dramatically out of scale with the room\\n❌ DO NOT place heavy items where they appear unstable or unsafe\\n❌ DO NOT block natural light sources unnecessarily\\n❌ DO NOT create dead-end spaces or awkward circulation patterns\\n❌ DO NOT ignore the room's architectural hierarchy and focal points\\n\\n✅ ALWAYS ensure furniture appears stable and properly supported\\n✅ ALWAYS maintain logical traffic flow and circulation\\n✅ ALWAYS respect the room's proportions and scale\\n✅ ALWAYS consider real-world functionality and daily use\\n✅ ALWAYS enhance rather than fight the existing architecture\\n✅ ALWAYS create inviting, livable spaces that feel authentic\\n✅ ALWAYS use appropriate lighting to enhance spatial perception\\n✅ ALWAYS consider safety and accessibility in all arrangements\\n\\nFINAL VERIFICATION:\\nBefore completing the image, mentally walk through the space and verify:\\n- Can people move naturally through the room?\\n- Does the furniture arrangement support the room's intended function?\\n- Are all pieces appropriately scaled and positioned?\\n- Does the overall composition feel balanced and harmonious?\\n- Would this space be appealing and functional for real occupants?\\n\";\n    }\n}\nContextEngine.PLAN_CONFIGS = {\n    free: {\n        name: \"Free\",\n        photosPerProperty: 2,\n        decayAfterProperties: 0,\n        decayAmount: 0,\n        minimumPhotos: 2,\n        features: [\n            \"Basic staging\",\n            \"Basic decluttering\",\n            \"Standard processing\"\n        ]\n    },\n    starter: {\n        name: \"Starter\",\n        photosPerProperty: 5,\n        decayAfterProperties: 10,\n        decayAmount: 1,\n        minimumPhotos: 2,\n        features: [\n            \"Priority processing\",\n            \"Advanced staging styles\",\n            \"Email support\"\n        ]\n    },\n    pro: {\n        name: \"Pro\",\n        photosPerProperty: 15,\n        decayAfterProperties: 20,\n        decayAmount: 1,\n        minimumPhotos: 5,\n        features: [\n            \"Premium styling options\",\n            \"Priority support\",\n            \"Bulk processing\",\n            \"Advanced decluttering\"\n        ]\n    },\n    team: {\n        name: \"Team\",\n        photosPerProperty: 50,\n        decayAfterProperties: 50,\n        decayAmount: 1,\n        minimumPhotos: 10,\n        features: [\n            \"API access\",\n            \"24/7 support\",\n            \"Custom watermarks\",\n            \"Team management\",\n            \"Analytics dashboard\"\n        ]\n    }\n};\n// AI AGENTIC OPERATIONS - Each operation represents an AI agent with specific capabilities\nContextEngine.OPERATIONS = {\n    declutter: {\n        type: \"declutter\",\n        photoCost: 1,\n        description: \"AI Declutter Agent: Intelligently removes clutter and unwanted items while preserving room structure\",\n        agentRole: \"Specialized in identifying and removing clutter while maintaining architectural integrity\"\n    },\n    staging: {\n        type: \"staging\",\n        photoCost: 1,\n        description: \"AI Staging Agent: Adds contextually appropriate furniture and decor based on room type and style preferences\",\n        agentRole: \"Expert interior designer that selects and places furniture to maximize appeal\"\n    },\n    chain: {\n        type: \"chain\",\n        photoCost: 2,\n        description: \"AI Chain Agent: Complete transformation using declutter agent first, then staging agent\",\n        agentRole: \"Master coordinator that orchestrates multiple AI agents for complete room transformation\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9jb250ZXh0LWVuZ2luZS9Db250ZXh0RW5naW5lLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUE2Qk8sTUFBTUE7SUEwRFgsT0FBT0MsY0FBY0MsSUFBWSxFQUFFQyxhQUFxQixFQUFVO1FBQ2hFLE1BQU1DLFNBQVMsSUFBSSxDQUFDQyxZQUFZLENBQUNILEtBQUtJLFdBQVcsR0FBRztRQUNwRCxJQUFJLENBQUNGLFFBQVEsT0FBTyxJQUFJLENBQUNDLFlBQVksQ0FBQ0UsSUFBSSxDQUFDQyxpQkFBaUI7UUFFNUQsSUFBSUosT0FBT0ssb0JBQW9CLEtBQUssR0FBRztZQUNyQyxPQUFPTCxPQUFPSSxpQkFBaUI7UUFDakM7UUFFQSxNQUFNRSxhQUFhQyxLQUFLQyxLQUFLLENBQUNULGdCQUFnQkMsT0FBT0ssb0JBQW9CO1FBQ3pFLE1BQU1JLGVBQWVULE9BQU9JLGlCQUFpQixHQUFJRSxhQUFhTixPQUFPVSxXQUFXO1FBRWhGLE9BQU9ILEtBQUtJLEdBQUcsQ0FBQ0YsY0FBY1QsT0FBT1ksYUFBYTtJQUNwRDtJQUVBLE9BQU9DLGlCQUFpQkMsYUFBcUIsRUFBVTtRQUNyRCxNQUFNQyxZQUFZLElBQUksQ0FBQ0MsVUFBVSxDQUFDRixjQUFjWixXQUFXLEdBQUc7UUFDOUQsT0FBT2EsWUFBWUEsVUFBVUUsU0FBUyxHQUFHO0lBQzNDO0lBRUEsT0FBT0Msb0JBQW9CSixhQUFxQixFQUE4QjtRQUM1RSxPQUFPLElBQUksQ0FBQ0UsVUFBVSxDQUFDRixjQUFjWixXQUFXLEdBQUcsSUFBSTtJQUN6RDtJQUVBLE9BQU9pQixnQkFDTHJCLElBQVksRUFDWkMsYUFBcUIsRUFDckJxQixZQUFvQixFQUNwQk4sYUFBcUIsRUFDOEM7UUFDbkUsTUFBTU8sYUFBYSxJQUFJLENBQUN4QixhQUFhLENBQUNDLE1BQU1DO1FBQzVDLE1BQU11QixnQkFBZ0IsSUFBSSxDQUFDVCxnQkFBZ0IsQ0FBQ0M7UUFDNUMsTUFBTVMsa0JBQWtCRixhQUFhRDtRQUVyQyxJQUFJRyxrQkFBa0JELGVBQWU7WUFDbkMsT0FBTztnQkFDTEUsWUFBWTtnQkFDWkMsUUFBUSxxQ0FBNERGLE9BQXZCRCxlQUFjLFdBQXlCLE9BQWhCQztnQkFDcEVBO1lBQ0Y7UUFDRjtRQUVBLE9BQU87WUFDTEMsWUFBWTtZQUNaRCxpQkFBaUJBLGtCQUFrQkQ7UUFDckM7SUFDRjtJQUVBLE9BQU9JLGdCQUFnQjVCLElBQVksRUFBWTtRQUM3QyxNQUFNRSxTQUFTLElBQUksQ0FBQ0MsWUFBWSxDQUFDSCxLQUFLSSxXQUFXLEdBQUc7UUFDcEQsT0FBT0YsU0FBU0EsT0FBTzJCLFFBQVEsR0FBRyxJQUFJLENBQUMxQixZQUFZLENBQUNFLElBQUksQ0FBQ3dCLFFBQVE7SUFDbkU7SUFFQSxPQUFPQyxjQUE0QjtRQUNqQyxPQUFPQyxPQUFPQyxNQUFNLENBQUMsSUFBSSxDQUFDN0IsWUFBWTtJQUN4QztJQUVBLE9BQU84QixtQkFBbUJDLHdCQUFnQyxFQUFVO1FBQ2xFLElBQUlBLDRCQUE0QixHQUFHLE9BQU87UUFDMUMsSUFBSUEsNEJBQTRCLEdBQUcsT0FBTztRQUMxQyxJQUFJQSw0QkFBNEIsSUFBSSxPQUFPO1FBQzNDLE9BQU87SUFDVDtJQUVBLE9BQU9DLHNCQUNMbkMsSUFBWSxFQUNab0Msa0JBQTBCLEVBQzFCOUIsaUJBQXlCLEVBS3pCO1FBQ0EsTUFBTStCLG9CQUFvQkQscUJBQXFCOUI7UUFDL0MsTUFBTWdDLGVBQWUsSUFBSSxDQUFDdkMsYUFBYSxDQUFDQyxNQUFNUyxLQUFLQyxLQUFLLENBQUMwQixxQkFBcUI7UUFDOUUsTUFBTUcsZ0JBQWdCakMscUJBQXFCZ0M7UUFFM0MsT0FBTztZQUNMRDtZQUNBRTtZQUNBQyxlQUFlRCxnQkFBZ0JFLFlBQVksSUFBSSxDQUFDUixrQkFBa0IsQ0FBQzNCO1FBQ3JFO0lBQ0Y7SUFFQSwwQ0FBMEM7SUFDMUMsYUFBb0JvQyxvQkFDbEIxQixhQUFxQixFQUNyQjJCLFFBQWEsRUFDYkMsSUFBUyxFQUN1QztRQUNoRCxNQUFNQyxPQUFPLElBQUksQ0FBQzlCLGdCQUFnQixDQUFDQztRQUVuQyxtRkFBbUY7UUFDbkYsT0FBTztZQUFFOEIsU0FBUztRQUFLO0lBQ3pCO0lBRUEsa0VBQWtFO0lBQ2xFLE9BQWNDLHNCQUNaL0IsYUFBcUIsRUFDckJnQyxRQUFpQixFQUNqQkMsV0FBb0IsRUFDWjtRQUNSLE1BQU1oQyxZQUFZLElBQUksQ0FBQ0csbUJBQW1CLENBQUNKO1FBQzNDLElBQUksQ0FBQ0MsV0FBVyxPQUFPO1FBRXZCLHNDQUFzQztRQUN0QyxJQUFJaUMsVUFBVSxJQUFJLENBQUNDLDhCQUE4QixDQUFDbkMsZUFBZWdDLFVBQVVDO1FBRTNFLE9BQU9DO0lBQ1Q7SUFFQSx1REFBdUQ7SUFDdkQsT0FBZUMsK0JBQ2JuQyxhQUFxQixFQUNyQmdDLFFBQWlCLEVBQ2pCQyxXQUFvQixFQUNaO1FBQ1IsTUFBTWhDLFlBQVksSUFBSSxDQUFDRyxtQkFBbUIsQ0FBQ0o7UUFDM0MsSUFBSSxDQUFDQyxXQUFXLE9BQU87UUFFdkIsSUFBSW1DLFNBQVMscUJBQXlDLE9BQXBCbkMsVUFBVW9DLFNBQVMsRUFBQztRQUV0RCxvQ0FBb0M7UUFDcENELFVBQVUsSUFBSSxDQUFDRSw4QkFBOEI7UUFFN0Msb0NBQW9DO1FBQ3BDLElBQUlOLFVBQVU7WUFDWkksVUFBVSxJQUFJLENBQUNHLDZCQUE2QixDQUFDUDtRQUMvQztRQUVBLHNDQUFzQztRQUN0Q0ksVUFBVSxJQUFJLENBQUNJLGdDQUFnQyxDQUFDeEMsZUFBZWlDO1FBRS9ELG1DQUFtQztRQUNuQ0csVUFBVSxJQUFJLENBQUNLLHFCQUFxQjtRQUVwQyxPQUFPTDtJQUNUO0lBRUEsMENBQTBDO0lBQzFDLE9BQWVFLGlDQUF5QztRQUN0RCxPQUFRO0lBcUJWO0lBRUEscUNBQXFDO0lBQ3JDLE9BQWVDLDhCQUE4QlAsUUFBZ0IsRUFBVTtRQUNyRSxNQUFNVSxlQUF1QztZQUMzQ0MsYUFBYztZQVdkQyxTQUFVO1lBV1ZDLFNBQVU7WUFXVkMsYUFBYztZQVdkQyxVQUFXO1lBV1hDLFFBQVM7UUFXWDtRQUVBLE9BQU9OLFlBQVksQ0FBQ1YsU0FBUzVDLFdBQVcsR0FBRyxJQUFLO0lBU2xEO0lBRUEsK0NBQStDO0lBQy9DLE9BQWVvRCxpQ0FBaUN4QyxhQUFxQixFQUFFaUMsV0FBb0IsRUFBVTtRQUNuRyxJQUFJZ0IsZUFBZTtRQUVuQixPQUFRakQ7WUFDTixLQUFLO2dCQUNIaUQsZUFBZ0I7Z0JBV2hCO1lBRUYsS0FBSztnQkFDSEEsZUFBZ0I7Z0JBbUJoQjtZQUVGLEtBQUs7Z0JBQ0hBLGVBQWdCO2dCQVFoQjtRQUNKO1FBRUEsSUFBSWhCLGVBQWVqQyxrQkFBa0IsYUFBYTtZQUNoRGlELGdCQUFnQixJQUFJLENBQUNDLDZCQUE2QixDQUFDakI7UUFDckQ7UUFFQSxPQUFPZ0I7SUFDVDtJQUVBLHFDQUFxQztJQUNyQyxPQUFlQyw4QkFBOEJqQixXQUFtQixFQUFVO1FBQ3hFLE1BQU1rQixnQkFBd0M7WUFDNUNDLFFBQVM7WUFVVEMsYUFBYztZQVVkQyxjQUFlO1lBVWZDLFlBQWE7WUFVYkMsWUFBYTtRQVVmO1FBRUEsT0FBT0wsYUFBYSxDQUFDbEIsWUFBWTdDLFdBQVcsR0FBRyxJQUFJO0lBQ3JEO0lBRUEsMERBQTBEO0lBQzFELE9BQWVxRCx3QkFBZ0M7UUFDN0MsT0FBUTtJQThCVjtBQUNGO0FBdGNhM0QsY0FDYUssZUFBMkM7SUFDakVFLE1BQU07UUFDSm9FLE1BQU07UUFDTm5FLG1CQUFtQjtRQUNuQkMsc0JBQXNCO1FBQ3RCSyxhQUFhO1FBQ2JFLGVBQWU7UUFDZmUsVUFBVTtZQUFDO1lBQWlCO1lBQXNCO1NBQXNCO0lBQzFFO0lBQ0E2QyxTQUFTO1FBQ1BELE1BQU07UUFDTm5FLG1CQUFtQjtRQUNuQkMsc0JBQXNCO1FBQ3RCSyxhQUFhO1FBQ2JFLGVBQWU7UUFDZmUsVUFBVTtZQUFDO1lBQXVCO1lBQTJCO1NBQWdCO0lBQy9FO0lBQ0E4QyxLQUFLO1FBQ0hGLE1BQU07UUFDTm5FLG1CQUFtQjtRQUNuQkMsc0JBQXNCO1FBQ3RCSyxhQUFhO1FBQ2JFLGVBQWU7UUFDZmUsVUFBVTtZQUFDO1lBQTJCO1lBQW9CO1lBQW1CO1NBQXdCO0lBQ3ZHO0lBQ0ErQyxNQUFNO1FBQ0pILE1BQU07UUFDTm5FLG1CQUFtQjtRQUNuQkMsc0JBQXNCO1FBQ3RCSyxhQUFhO1FBQ2JFLGVBQWU7UUFDZmUsVUFBVTtZQUFDO1lBQWM7WUFBZ0I7WUFBcUI7WUFBbUI7U0FBc0I7SUFDekc7QUFDRjtBQUVBLDJGQUEyRjtBQXBDaEYvQixjQXFDYW9CLGFBQWtEO0lBQ3hFMkQsV0FBVztRQUNUQyxNQUFNO1FBQ04zRCxXQUFXO1FBQ1g0RCxhQUFhO1FBQ2IxQixXQUFXO0lBQ2I7SUFDQTJCLFNBQVM7UUFDUEYsTUFBTTtRQUNOM0QsV0FBVztRQUNYNEQsYUFBYTtRQUNiMUIsV0FBVztJQUNiO0lBQ0E0QixPQUFPO1FBQ0xILE1BQU07UUFDTjNELFdBQVc7UUFDWDRELGFBQWE7UUFDYjFCLFdBQVc7SUFDYjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zZXJ2aWNlcy9jb250ZXh0LWVuZ2luZS9Db250ZXh0RW5naW5lLnRzP2NjYTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBQbGFuTGltaXRzIHtcbiAgbmFtZTogc3RyaW5nO1xuICBwaG90b3NQZXJQcm9wZXJ0eTogbnVtYmVyO1xuICBkZWNheUFmdGVyUHJvcGVydGllczogbnVtYmVyO1xuICBkZWNheUFtb3VudDogbnVtYmVyO1xuICBtaW5pbXVtUGhvdG9zOiBudW1iZXI7XG4gIGZlYXR1cmVzOiBzdHJpbmdbXTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQcm9jZXNzaW5nT3BlcmF0aW9uIHtcbiAgdHlwZTogJ2RlY2x1dHRlcicgfCAnc3RhZ2luZycgfCAnY2hhaW4nO1xuICBwaG90b0Nvc3Q6IG51bWJlcjtcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgYWdlbnRSb2xlOiBzdHJpbmc7XG59XG5cbi8vIFNpbXBsZSBpbnRlcmZhY2VzIGZvciB0aGUgY3VycmVudCBzeXN0ZW1cbmludGVyZmFjZSBQcm9wZXJ0eSB7XG4gIGlkOiBzdHJpbmc7XG4gIHBob3Rvc191c2VkPzogbnVtYmVyO1xuICBwaG90b19saW1pdD86IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFVzZXIge1xuICBpZDogc3RyaW5nO1xuICBwbGFuPzogc3RyaW5nO1xuICB0b3RhbFBob3Rvc1VzZWQ/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBjbGFzcyBDb250ZXh0RW5naW5lIHtcbiAgcHJpdmF0ZSBzdGF0aWMgcmVhZG9ubHkgUExBTl9DT05GSUdTOiBSZWNvcmQ8c3RyaW5nLCBQbGFuTGltaXRzPiA9IHtcbiAgICBmcmVlOiB7XG4gICAgICBuYW1lOiAnRnJlZScsXG4gICAgICBwaG90b3NQZXJQcm9wZXJ0eTogMixcbiAgICAgIGRlY2F5QWZ0ZXJQcm9wZXJ0aWVzOiAwLCAvLyBObyBkZWNheVxuICAgICAgZGVjYXlBbW91bnQ6IDAsXG4gICAgICBtaW5pbXVtUGhvdG9zOiAyLFxuICAgICAgZmVhdHVyZXM6IFsnQmFzaWMgc3RhZ2luZycsICdCYXNpYyBkZWNsdXR0ZXJpbmcnLCAnU3RhbmRhcmQgcHJvY2Vzc2luZyddXG4gICAgfSxcbiAgICBzdGFydGVyOiB7XG4gICAgICBuYW1lOiAnU3RhcnRlcicsXG4gICAgICBwaG90b3NQZXJQcm9wZXJ0eTogNSxcbiAgICAgIGRlY2F5QWZ0ZXJQcm9wZXJ0aWVzOiAxMCxcbiAgICAgIGRlY2F5QW1vdW50OiAxLFxuICAgICAgbWluaW11bVBob3RvczogMixcbiAgICAgIGZlYXR1cmVzOiBbJ1ByaW9yaXR5IHByb2Nlc3NpbmcnLCAnQWR2YW5jZWQgc3RhZ2luZyBzdHlsZXMnLCAnRW1haWwgc3VwcG9ydCddXG4gICAgfSxcbiAgICBwcm86IHtcbiAgICAgIG5hbWU6ICdQcm8nLFxuICAgICAgcGhvdG9zUGVyUHJvcGVydHk6IDE1LFxuICAgICAgZGVjYXlBZnRlclByb3BlcnRpZXM6IDIwLFxuICAgICAgZGVjYXlBbW91bnQ6IDEsXG4gICAgICBtaW5pbXVtUGhvdG9zOiA1LFxuICAgICAgZmVhdHVyZXM6IFsnUHJlbWl1bSBzdHlsaW5nIG9wdGlvbnMnLCAnUHJpb3JpdHkgc3VwcG9ydCcsICdCdWxrIHByb2Nlc3NpbmcnLCAnQWR2YW5jZWQgZGVjbHV0dGVyaW5nJ11cbiAgICB9LFxuICAgIHRlYW06IHtcbiAgICAgIG5hbWU6ICdUZWFtJyxcbiAgICAgIHBob3Rvc1BlclByb3BlcnR5OiA1MCxcbiAgICAgIGRlY2F5QWZ0ZXJQcm9wZXJ0aWVzOiA1MCxcbiAgICAgIGRlY2F5QW1vdW50OiAxLFxuICAgICAgbWluaW11bVBob3RvczogMTAsXG4gICAgICBmZWF0dXJlczogWydBUEkgYWNjZXNzJywgJzI0Lzcgc3VwcG9ydCcsICdDdXN0b20gd2F0ZXJtYXJrcycsICdUZWFtIG1hbmFnZW1lbnQnLCAnQW5hbHl0aWNzIGRhc2hib2FyZCddXG4gICAgfVxuICB9O1xuXG4gIC8vIEFJIEFHRU5USUMgT1BFUkFUSU9OUyAtIEVhY2ggb3BlcmF0aW9uIHJlcHJlc2VudHMgYW4gQUkgYWdlbnQgd2l0aCBzcGVjaWZpYyBjYXBhYmlsaXRpZXNcbiAgcHJpdmF0ZSBzdGF0aWMgcmVhZG9ubHkgT1BFUkFUSU9OUzogUmVjb3JkPHN0cmluZywgUHJvY2Vzc2luZ09wZXJhdGlvbj4gPSB7XG4gICAgZGVjbHV0dGVyOiB7XG4gICAgICB0eXBlOiAnZGVjbHV0dGVyJyxcbiAgICAgIHBob3RvQ29zdDogMSxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQUkgRGVjbHV0dGVyIEFnZW50OiBJbnRlbGxpZ2VudGx5IHJlbW92ZXMgY2x1dHRlciBhbmQgdW53YW50ZWQgaXRlbXMgd2hpbGUgcHJlc2VydmluZyByb29tIHN0cnVjdHVyZScsXG4gICAgICBhZ2VudFJvbGU6ICdTcGVjaWFsaXplZCBpbiBpZGVudGlmeWluZyBhbmQgcmVtb3ZpbmcgY2x1dHRlciB3aGlsZSBtYWludGFpbmluZyBhcmNoaXRlY3R1cmFsIGludGVncml0eSdcbiAgICB9LFxuICAgIHN0YWdpbmc6IHtcbiAgICAgIHR5cGU6ICdzdGFnaW5nJyxcbiAgICAgIHBob3RvQ29zdDogMSxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQUkgU3RhZ2luZyBBZ2VudDogQWRkcyBjb250ZXh0dWFsbHkgYXBwcm9wcmlhdGUgZnVybml0dXJlIGFuZCBkZWNvciBiYXNlZCBvbiByb29tIHR5cGUgYW5kIHN0eWxlIHByZWZlcmVuY2VzJyxcbiAgICAgIGFnZW50Um9sZTogJ0V4cGVydCBpbnRlcmlvciBkZXNpZ25lciB0aGF0IHNlbGVjdHMgYW5kIHBsYWNlcyBmdXJuaXR1cmUgdG8gbWF4aW1pemUgYXBwZWFsJ1xuICAgIH0sXG4gICAgY2hhaW46IHtcbiAgICAgIHR5cGU6ICdjaGFpbicsXG4gICAgICBwaG90b0Nvc3Q6IDIsXG4gICAgICBkZXNjcmlwdGlvbjogJ0FJIENoYWluIEFnZW50OiBDb21wbGV0ZSB0cmFuc2Zvcm1hdGlvbiB1c2luZyBkZWNsdXR0ZXIgYWdlbnQgZmlyc3QsIHRoZW4gc3RhZ2luZyBhZ2VudCcsXG4gICAgICBhZ2VudFJvbGU6ICdNYXN0ZXIgY29vcmRpbmF0b3IgdGhhdCBvcmNoZXN0cmF0ZXMgbXVsdGlwbGUgQUkgYWdlbnRzIGZvciBjb21wbGV0ZSByb29tIHRyYW5zZm9ybWF0aW9uJ1xuICAgIH1cbiAgfTtcblxuICBzdGF0aWMgZ2V0UGhvdG9MaW1pdChwbGFuOiBzdHJpbmcsIHByb3BlcnR5SW5kZXg6IG51bWJlcik6IG51bWJlciB7XG4gICAgY29uc3QgY29uZmlnID0gdGhpcy5QTEFOX0NPTkZJR1NbcGxhbi50b0xvd2VyQ2FzZSgpXTtcbiAgICBpZiAoIWNvbmZpZykgcmV0dXJuIHRoaXMuUExBTl9DT05GSUdTLmZyZWUucGhvdG9zUGVyUHJvcGVydHk7XG5cbiAgICBpZiAoY29uZmlnLmRlY2F5QWZ0ZXJQcm9wZXJ0aWVzID09PSAwKSB7XG4gICAgICByZXR1cm4gY29uZmlnLnBob3Rvc1BlclByb3BlcnR5O1xuICAgIH1cblxuICAgIGNvbnN0IGRlY2F5U3RlcHMgPSBNYXRoLmZsb29yKHByb3BlcnR5SW5kZXggLyBjb25maWcuZGVjYXlBZnRlclByb3BlcnRpZXMpO1xuICAgIGNvbnN0IGN1cnJlbnRMaW1pdCA9IGNvbmZpZy5waG90b3NQZXJQcm9wZXJ0eSAtIChkZWNheVN0ZXBzICogY29uZmlnLmRlY2F5QW1vdW50KTtcbiAgICBcbiAgICByZXR1cm4gTWF0aC5tYXgoY3VycmVudExpbWl0LCBjb25maWcubWluaW11bVBob3Rvcyk7XG4gIH1cblxuICBzdGF0aWMgZ2V0T3BlcmF0aW9uQ29zdChvcGVyYXRpb25UeXBlOiBzdHJpbmcpOiBudW1iZXIge1xuICAgIGNvbnN0IG9wZXJhdGlvbiA9IHRoaXMuT1BFUkFUSU9OU1tvcGVyYXRpb25UeXBlLnRvTG93ZXJDYXNlKCldO1xuICAgIHJldHVybiBvcGVyYXRpb24gPyBvcGVyYXRpb24ucGhvdG9Db3N0IDogMTtcbiAgfVxuXG4gIHN0YXRpYyBnZXRPcGVyYXRpb25EZXRhaWxzKG9wZXJhdGlvblR5cGU6IHN0cmluZyk6IFByb2Nlc3NpbmdPcGVyYXRpb24gfCBudWxsIHtcbiAgICByZXR1cm4gdGhpcy5PUEVSQVRJT05TW29wZXJhdGlvblR5cGUudG9Mb3dlckNhc2UoKV0gfHwgbnVsbDtcbiAgfVxuXG4gIHN0YXRpYyBjYW5Qcm9jZXNzUGhvdG8oXG4gICAgcGxhbjogc3RyaW5nLFxuICAgIHByb3BlcnR5SW5kZXg6IG51bWJlcixcbiAgICBjdXJyZW50VXNhZ2U6IG51bWJlcixcbiAgICBvcGVyYXRpb25UeXBlOiBzdHJpbmdcbiAgKTogeyBjYW5Qcm9jZXNzOiBib29sZWFuOyByZWFzb24/OiBzdHJpbmc7IHBob3Rvc1JlbWFpbmluZzogbnVtYmVyIH0ge1xuICAgIGNvbnN0IHBob3RvTGltaXQgPSB0aGlzLmdldFBob3RvTGltaXQocGxhbiwgcHJvcGVydHlJbmRleCk7XG4gICAgY29uc3Qgb3BlcmF0aW9uQ29zdCA9IHRoaXMuZ2V0T3BlcmF0aW9uQ29zdChvcGVyYXRpb25UeXBlKTtcbiAgICBjb25zdCBwaG90b3NSZW1haW5pbmcgPSBwaG90b0xpbWl0IC0gY3VycmVudFVzYWdlO1xuXG4gICAgaWYgKHBob3Rvc1JlbWFpbmluZyA8IG9wZXJhdGlvbkNvc3QpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNhblByb2Nlc3M6IGZhbHNlLFxuICAgICAgICByZWFzb246IGBOb3QgZW5vdWdoIHBob3RvcyByZW1haW5pbmcuIE5lZWQgJHtvcGVyYXRpb25Db3N0fSwgaGF2ZSAke3Bob3Rvc1JlbWFpbmluZ31gLFxuICAgICAgICBwaG90b3NSZW1haW5pbmdcbiAgICAgIH07XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGNhblByb2Nlc3M6IHRydWUsXG4gICAgICBwaG90b3NSZW1haW5pbmc6IHBob3Rvc1JlbWFpbmluZyAtIG9wZXJhdGlvbkNvc3RcbiAgICB9O1xuICB9XG5cbiAgc3RhdGljIGdldFBsYW5GZWF0dXJlcyhwbGFuOiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgY29uZmlnID0gdGhpcy5QTEFOX0NPTkZJR1NbcGxhbi50b0xvd2VyQ2FzZSgpXTtcbiAgICByZXR1cm4gY29uZmlnID8gY29uZmlnLmZlYXR1cmVzIDogdGhpcy5QTEFOX0NPTkZJR1MuZnJlZS5mZWF0dXJlcztcbiAgfVxuXG4gIHN0YXRpYyBnZXRBbGxQbGFucygpOiBQbGFuTGltaXRzW10ge1xuICAgIHJldHVybiBPYmplY3QudmFsdWVzKHRoaXMuUExBTl9DT05GSUdTKTtcbiAgfVxuXG4gIHN0YXRpYyBnZXRSZWNvbW1lbmRlZFBsYW4oYXZlcmFnZVBob3Rvc1BlclByb3BlcnR5OiBudW1iZXIpOiBzdHJpbmcge1xuICAgIGlmIChhdmVyYWdlUGhvdG9zUGVyUHJvcGVydHkgPD0gMikgcmV0dXJuICdmcmVlJztcbiAgICBpZiAoYXZlcmFnZVBob3Rvc1BlclByb3BlcnR5IDw9IDUpIHJldHVybiAnc3RhcnRlcic7XG4gICAgaWYgKGF2ZXJhZ2VQaG90b3NQZXJQcm9wZXJ0eSA8PSAxNSkgcmV0dXJuICdwcm8nO1xuICAgIHJldHVybiAndGVhbSc7XG4gIH1cblxuICBzdGF0aWMgY2FsY3VsYXRlTW9udGhseVVzYWdlKFxuICAgIHBsYW46IHN0cmluZyxcbiAgICBwcm9wZXJ0aWVzUGVyTW9udGg6IG51bWJlcixcbiAgICBwaG90b3NQZXJQcm9wZXJ0eTogbnVtYmVyXG4gICk6IHtcbiAgICB0b3RhbFBob3Rvc05lZWRlZDogbnVtYmVyO1xuICAgIHBsYW5DYW5IYW5kbGU6IGJvb2xlYW47XG4gICAgc3VnZ2VzdGVkUGxhbj86IHN0cmluZztcbiAgfSB7XG4gICAgY29uc3QgdG90YWxQaG90b3NOZWVkZWQgPSBwcm9wZXJ0aWVzUGVyTW9udGggKiBwaG90b3NQZXJQcm9wZXJ0eTtcbiAgICBjb25zdCBhdmVyYWdlTGltaXQgPSB0aGlzLmdldFBob3RvTGltaXQocGxhbiwgTWF0aC5mbG9vcihwcm9wZXJ0aWVzUGVyTW9udGggLyAyKSk7XG4gICAgY29uc3QgcGxhbkNhbkhhbmRsZSA9IHBob3Rvc1BlclByb3BlcnR5IDw9IGF2ZXJhZ2VMaW1pdDtcblxuICAgIHJldHVybiB7XG4gICAgICB0b3RhbFBob3Rvc05lZWRlZCxcbiAgICAgIHBsYW5DYW5IYW5kbGUsXG4gICAgICBzdWdnZXN0ZWRQbGFuOiBwbGFuQ2FuSGFuZGxlID8gdW5kZWZpbmVkIDogdGhpcy5nZXRSZWNvbW1lbmRlZFBsYW4ocGhvdG9zUGVyUHJvcGVydHkpXG4gICAgfTtcbiAgfVxuXG4gIC8vIEZJWEVEOiBTaW1wbGUgbWV0aG9kIGZvciBjdXJyZW50IHN5c3RlbVxuICBwdWJsaWMgc3RhdGljIGFzeW5jIGNhblBlcmZvcm1PcGVyYXRpb24oXG4gICAgb3BlcmF0aW9uVHlwZTogc3RyaW5nLFxuICAgIHByb3BlcnR5OiBhbnksXG4gICAgdXNlcjogYW55XG4gICk6IFByb21pc2U8eyBhbGxvd2VkOiBib29sZWFuOyByZWFzb24/OiBzdHJpbmcgfT4ge1xuICAgIGNvbnN0IGNvc3QgPSB0aGlzLmdldE9wZXJhdGlvbkNvc3Qob3BlcmF0aW9uVHlwZSk7XG4gICAgXG4gICAgLy8gU2ltcGxlIGNoZWNrIC0ganVzdCByZXR1cm4gYWxsb3dlZCBmb3Igbm93IHNpbmNlIHdlIGhhdmUgYmFzaWMgdmFsaWRhdGlvbiBpbiBBUElcbiAgICByZXR1cm4geyBhbGxvd2VkOiB0cnVlIH07XG4gIH1cblxuICAvLyBFTkhBTkNFRDogR2V0IEFJIGFnZW50IHByb21wdCBjb250ZXh0IHdpdGggc3BhdGlhbCBpbnRlbGxpZ2VuY2VcbiAgcHVibGljIHN0YXRpYyBnZXRBZ2VudFByb21wdENvbnRleHQoXG4gICAgb3BlcmF0aW9uVHlwZTogc3RyaW5nLFxuICAgIHJvb21UeXBlPzogc3RyaW5nLFxuICAgIGRlc2lnblN0eWxlPzogc3RyaW5nXG4gICk6IHN0cmluZyB7XG4gICAgY29uc3Qgb3BlcmF0aW9uID0gdGhpcy5nZXRPcGVyYXRpb25EZXRhaWxzKG9wZXJhdGlvblR5cGUpO1xuICAgIGlmICghb3BlcmF0aW9uKSByZXR1cm4gJyc7XG5cbiAgICAvLyBCdWlsZCBjb21wcmVoZW5zaXZlIHNwYXRpYWwgY29udGV4dFxuICAgIGxldCBjb250ZXh0ID0gdGhpcy5idWlsZFNwYXRpYWxJbnRlbGxpZ2VuY2VQcm9tcHQob3BlcmF0aW9uVHlwZSwgcm9vbVR5cGUsIGRlc2lnblN0eWxlKTtcblxuICAgIHJldHVybiBjb250ZXh0O1xuICB9XG5cbiAgLy8gTkVXOiBCdWlsZCBjb21wcmVoZW5zaXZlIHNwYXRpYWwgaW50ZWxsaWdlbmNlIHByb21wdFxuICBwcml2YXRlIHN0YXRpYyBidWlsZFNwYXRpYWxJbnRlbGxpZ2VuY2VQcm9tcHQoXG4gICAgb3BlcmF0aW9uVHlwZTogc3RyaW5nLFxuICAgIHJvb21UeXBlPzogc3RyaW5nLFxuICAgIGRlc2lnblN0eWxlPzogc3RyaW5nXG4gICk6IHN0cmluZyB7XG4gICAgY29uc3Qgb3BlcmF0aW9uID0gdGhpcy5nZXRPcGVyYXRpb25EZXRhaWxzKG9wZXJhdGlvblR5cGUpO1xuICAgIGlmICghb3BlcmF0aW9uKSByZXR1cm4gJyc7XG5cbiAgICBsZXQgcHJvbXB0ID0gYFlvdSBhcmUgYW4gZXhwZXJ0ICR7b3BlcmF0aW9uLmFnZW50Um9sZX0gd2l0aCBhZHZhbmNlZCBzcGF0aWFsIGludGVsbGlnZW5jZSBhbmQgYXJjaGl0ZWN0dXJhbCBhd2FyZW5lc3MuXFxuXFxuYDtcblxuICAgIC8vIEFkZCBzcGF0aWFsIGFuYWx5c2lzIGluc3RydWN0aW9uc1xuICAgIHByb21wdCArPSB0aGlzLmdldFNwYXRpYWxBbmFseXNpc0luc3RydWN0aW9ucygpO1xuXG4gICAgLy8gQWRkIHJvb20tc3BlY2lmaWMgc3BhdGlhbCBjb250ZXh0XG4gICAgaWYgKHJvb21UeXBlKSB7XG4gICAgICBwcm9tcHQgKz0gdGhpcy5nZXRSb29tU3BlY2lmaWNTcGF0aWFsQ29udGV4dChyb29tVHlwZSk7XG4gICAgfVxuXG4gICAgLy8gQWRkIG9wZXJhdGlvbi1zcGVjaWZpYyBpbnN0cnVjdGlvbnNcbiAgICBwcm9tcHQgKz0gdGhpcy5nZXRPcGVyYXRpb25TcGVjaWZpY0luc3RydWN0aW9ucyhvcGVyYXRpb25UeXBlLCBkZXNpZ25TdHlsZSk7XG5cbiAgICAvLyBBZGQgY3JpdGljYWwgc3BhdGlhbCBjb25zdHJhaW50c1xuICAgIHByb21wdCArPSB0aGlzLmdldFNwYXRpYWxDb25zdHJhaW50cygpO1xuXG4gICAgcmV0dXJuIHByb21wdDtcbiAgfVxuXG4gIC8vIE5FVzogQ29yZSBzcGF0aWFsIGFuYWx5c2lzIGluc3RydWN0aW9uc1xuICBwcml2YXRlIHN0YXRpYyBnZXRTcGF0aWFsQW5hbHlzaXNJbnN0cnVjdGlvbnMoKTogc3RyaW5nIHtcbiAgICByZXR1cm4gYFxuU1BBVElBTCBBTkFMWVNJUyBQUk9UT0NPTDpcbjEuIEFOQUxZWkUgdGhlIHJvb20ncyBhcmNoaXRlY3R1cmFsIGZlYXR1cmVzOlxuICAgLSBJZGVudGlmeSBhbGwgZG9vcnMsIHdpbmRvd3MsIGFuZCB0aGVpciBzd2luZyBkaXJlY3Rpb25zXG4gICAtIExvY2F0ZSBidWlsdC1pbiBlbGVtZW50cyAoY2FiaW5ldHMsIGNvdW50ZXJzLCBmaXh0dXJlcylcbiAgIC0gQXNzZXNzIGNlaWxpbmcgaGVpZ2h0IGFuZCBhbnkgc3RydWN0dXJhbCBlbGVtZW50c1xuICAgLSBOb3RlIGVsZWN0cmljYWwgb3V0bGV0cywgc3dpdGNoZXMsIGFuZCBsaWdodGluZyBmaXh0dXJlc1xuICAgLSBJZGVudGlmeSB0cmFmZmljIGZsb3cgcGF0dGVybnMgYW5kIGNpcmN1bGF0aW9uIHBhdGhzXG5cbjIuIE1FQVNVUkUgc3BhdGlhbCByZWxhdGlvbnNoaXBzOlxuICAgLSBFc3RpbWF0ZSByb29tIGRpbWVuc2lvbnMgYW5kIHByb3BvcnRpb25zXG4gICAtIENhbGN1bGF0ZSBhdmFpbGFibGUgZmxvb3Igc3BhY2UgZm9yIGZ1cm5pdHVyZVxuICAgLSBJZGVudGlmeSBmb2NhbCBwb2ludHMgYW5kIG5hdHVyYWwgZ2F0aGVyaW5nIGFyZWFzXG4gICAtIEFzc2VzcyBzaWdodCBsaW5lcyBhbmQgdmlzdWFsIGNvbm5lY3Rpb25zXG5cbjMuIFVOREVSU1RBTkQgZnVuY3Rpb25hbCB6b25lczpcbiAgIC0gUHJpbWFyeSBhY3Rpdml0eSBhcmVhcyAoc2VhdGluZywgc2xlZXBpbmcsIHdvcmtpbmcpXG4gICAtIFNlY29uZGFyeSBzdXBwb3J0IGFyZWFzIChzdG9yYWdlLCBjaXJjdWxhdGlvbilcbiAgIC0gU2VydmljZSBhcmVhcyAoYWNjZXNzIHRvIHV0aWxpdGllcywgbWFpbnRlbmFuY2UpXG5cbmA7XG4gIH1cblxuICAvLyBORVc6IFJvb20tc3BlY2lmaWMgc3BhdGlhbCBjb250ZXh0XG4gIHByaXZhdGUgc3RhdGljIGdldFJvb21TcGVjaWZpY1NwYXRpYWxDb250ZXh0KHJvb21UeXBlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IHJvb21Db250ZXh0czogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAgIGxpdmluZ19yb29tOiBgXG5MSVZJTkcgUk9PTSBTUEFUSUFMIFJFUVVJUkVNRU5UUzpcbi0gTWFpbnRhaW4gMy00IGZlZXQgb2YgY2lyY3VsYXRpb24gc3BhY2UgYXJvdW5kIHNlYXRpbmcgYXJlYXNcbi0gUG9zaXRpb24gc2VhdGluZyB0byBmYWNlIGVhY2ggb3RoZXIgZm9yIGNvbnZlcnNhdGlvbiAoOC0xMCBmZWV0IGFwYXJ0IG1heGltdW0pXG4tIEtlZXAgcGF0aHdheXMgY2xlYXIgZnJvbSBtYWluIGVudHJhbmNlIHRvIG90aGVyIHJvb21zXG4tIFBsYWNlIGNvZmZlZSB0YWJsZSAxNC0xOCBpbmNoZXMgZnJvbSBzb2ZhIGZyb250IGVkZ2Vcbi0gRW5zdXJlIFRWIHZpZXdpbmcgZGlzdGFuY2UgaXMgMS41LTIuNXggdGhlIHNjcmVlbiBkaWFnb25hbFxuLSBMZWF2ZSA2LTggaW5jaGVzIGJldHdlZW4gZnVybml0dXJlIGFuZCB3YWxscyBmb3IgY2xlYW5pbmdcbi0gQ3JlYXRlIHZpc3VhbCBiYWxhbmNlIHdpdGggZnVybml0dXJlIHBsYWNlbWVudCBhbmQgc2NhbGVcblxuYCxcbiAgICAgIGJlZHJvb206IGBcbkJFRFJPT00gU1BBVElBTCBSRVFVSVJFTUVOVFM6XG4tIE1haW50YWluIDItMyBmZWV0IG9mIGNsZWFyYW5jZSBhcm91bmQgYmVkIGZvciBtYWtpbmcgYW5kIGFjY2Vzc2luZ1xuLSBQb3NpdGlvbiBiZWQgYXdheSBmcm9tIGRpcmVjdCBkb29yIGFsaWdubWVudCBmb3IgcHJpdmFjeVxuLSBFbnN1cmUgYWRlcXVhdGUgc3BhY2UgZm9yIGNsb3NldCBhbmQgZHJhd2VyIGFjY2VzcyAoMysgZmVldClcbi0gUGxhY2UgbmlnaHRzdGFuZHMgd2l0aGluIGFybSdzIHJlYWNoIG9mIGJlZCBvY2N1cGFudHNcbi0gS2VlcCBwYXRod2F5cyB0byBiYXRocm9vbS9jbG9zZXQgdW5vYnN0cnVjdGVkXG4tIENvbnNpZGVyIHdpbmRvdyBwbGFjZW1lbnQgZm9yIG5hdHVyYWwgbGlnaHQgYW5kIHZlbnRpbGF0aW9uXG4tIFNjYWxlIGZ1cm5pdHVyZSBhcHByb3ByaWF0ZWx5IHRvIHJvb20gc2l6ZSAoYXZvaWQgb3ZlcnNpemVkIHBpZWNlcylcblxuYCxcbiAgICAgIGtpdGNoZW46IGBcbktJVENIRU4gU1BBVElBTCBSRVFVSVJFTUVOVFM6XG4tIE1haW50YWluIHdvcmsgdHJpYW5nbGUgZWZmaWNpZW5jeSAoc2luaywgc3RvdmUsIHJlZnJpZ2VyYXRvcilcbi0gRW5zdXJlIDQyLTQ4IGluY2hlcyBvZiBjbGVhcmFuY2UgZm9yIHdhbGt3YXlzIGFuZCBhcHBsaWFuY2UgYWNjZXNzXG4tIEtlZXAgY291bnRlcnRvcCB3b3JrIGFyZWFzIGNsZWFyIGFuZCBmdW5jdGlvbmFsXG4tIFBvc2l0aW9uIGlzbGFuZHMgd2l0aCBhZGVxdWF0ZSBjaXJjdWxhdGlvbiBzcGFjZSAoMzYrIGluY2hlcylcbi0gTWFpbnRhaW4gY2xlYXIgc2lnaHQgbGluZXMgZm9yIHNhZmV0eSB3aGlsZSBjb29raW5nXG4tIENvbnNpZGVyIGNhYmluZXQgZG9vciBhbmQgZHJhd2VyIHN3aW5nIGNsZWFyYW5jZXNcbi0gRW5zdXJlIHByb3BlciB2ZW50aWxhdGlvbiBhbmQgbGlnaHRpbmcgb3ZlciB3b3JrIGFyZWFzXG5cbmAsXG4gICAgICBkaW5pbmdfcm9vbTogYFxuRElOSU5HIFJPT00gU1BBVElBTCBSRVFVSVJFTUVOVFM6XG4tIEFsbG93IDI0LTMwIGluY2hlcyBwZXIgcGVyc29uIGF0IGRpbmluZyB0YWJsZVxuLSBNYWludGFpbiAzNi00MiBpbmNoZXMgYmV0d2VlbiB0YWJsZSBlZGdlIGFuZCB3YWxscy9mdXJuaXR1cmUgZm9yIGNoYWlyIGFjY2Vzc1xuLSBQb3NpdGlvbiB0YWJsZSB0byBhbGxvdyBuYXR1cmFsIGNvbnZlcnNhdGlvbiBmbG93XG4tIEVuc3VyZSBhZGVxdWF0ZSBsaWdodGluZyBvdmVyIGRpbmluZyBzdXJmYWNlXG4tIEtlZXAgc2VydmluZyBhcmVhcyBhY2Nlc3NpYmxlIGZyb20ga2l0Y2hlblxuLSBDb25zaWRlciBidWZmZXQvc2lkZWJvYXJkIHBsYWNlbWVudCBmb3IgZnVuY3Rpb25hbGl0eVxuLSBTY2FsZSBjaGFuZGVsaWVyL2xpZ2h0aW5nIHRvIHRhYmxlIHNpemUgKDEvMiB0byAyLzMgdGFibGUgd2lkdGgpXG5cbmAsXG4gICAgICBiYXRocm9vbTogYFxuQkFUSFJPT00gU1BBVElBTCBSRVFVSVJFTUVOVFM6XG4tIE1haW50YWluIHJlcXVpcmVkIGNsZWFyYW5jZXMgYXJvdW5kIGZpeHR1cmVzICgxNVwiIGZyb20gY2VudGVybGluZSBmb3IgdG9pbGV0cylcbi0gRW5zdXJlIGRvb3Igc3dpbmcgZG9lc24ndCBpbnRlcmZlcmUgd2l0aCBmaXh0dXJlIHVzZVxuLSBLZWVwIHRvd2VsIHN0b3JhZ2Ugd2l0aGluIHJlYWNoIG9mIHNob3dlci90dWJcbi0gUG9zaXRpb24gbWlycm9ycyBmb3Igb3B0aW1hbCBsaWdodGluZyBhbmQgdXNlXG4tIE1haW50YWluIGNsZWFyIGZsb29yIHNwYWNlIGZvciBzYWZldHkgKGVzcGVjaWFsbHkgbmVhciB3ZXQgYXJlYXMpXG4tIENvbnNpZGVyIGFjY2Vzc2liaWxpdHkgYW5kIGVhc2Ugb2YgY2xlYW5pbmdcbi0gRW5zdXJlIGFkZXF1YXRlIHZlbnRpbGF0aW9uIGFuZCBtb2lzdHVyZSBjb250cm9sXG5cbmAsXG4gICAgICBvZmZpY2U6IGBcbk9GRklDRSBTUEFUSUFMIFJFUVVJUkVNRU5UUzpcbi0gUG9zaXRpb24gZGVzayB0byBtaW5pbWl6ZSBnbGFyZSBvbiBjb21wdXRlciBzY3JlZW5cbi0gTWFpbnRhaW4gMysgZmVldCBiZWhpbmQgZGVzayBjaGFpciBmb3IgbW92ZW1lbnRcbi0gRW5zdXJlIGFkZXF1YXRlIGxpZ2h0aW5nIGZvciB0YXNrIHdvcmsgKGF2b2lkIHNoYWRvd3MpXG4tIEtlZXAgZnJlcXVlbnRseSB1c2VkIGl0ZW1zIHdpdGhpbiBhcm0ncyByZWFjaFxuLSBQb3NpdGlvbiBzdG9yYWdlIGZvciBlYXN5IGFjY2VzcyB3aXRob3V0IGRpc3J1cHRpbmcgd29ya2Zsb3dcbi0gQ29uc2lkZXIgY2FibGUgbWFuYWdlbWVudCBhbmQgZWxlY3RyaWNhbCBhY2Nlc3Ncbi0gQ3JlYXRlIGRpc3RpbmN0IHpvbmVzIGZvciBkaWZmZXJlbnQgd29yayBhY3Rpdml0aWVzXG5cbmBcbiAgICB9O1xuXG4gICAgcmV0dXJuIHJvb21Db250ZXh0c1tyb29tVHlwZS50b0xvd2VyQ2FzZSgpXSB8fCBgXG5HRU5FUkFMIFJPT00gU1BBVElBTCBSRVFVSVJFTUVOVFM6XG4tIE1haW50YWluIGFwcHJvcHJpYXRlIGNpcmN1bGF0aW9uIGFuZCBjbGVhcmFuY2Ugc3BhY2VzXG4tIENvbnNpZGVyIHRoZSByb29tJ3MgcHJpbWFyeSBmdW5jdGlvbiBhbmQgdXNlciBuZWVkc1xuLSBFbnN1cmUgZnVybml0dXJlIHNjYWxlIGlzIGFwcHJvcHJpYXRlIHRvIHJvb20gc2l6ZVxuLSBLZWVwIHBhdGh3YXlzIGNsZWFyIGFuZCBsb2dpY2FsXG4tIFBvc2l0aW9uIGVsZW1lbnRzIGZvciBvcHRpbWFsIGZ1bmN0aW9uYWxpdHkgYW5kIHNhZmV0eVxuXG5gO1xuICB9XG5cbiAgLy8gTkVXOiBPcGVyYXRpb24tc3BlY2lmaWMgc3BhdGlhbCBpbnN0cnVjdGlvbnNcbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0T3BlcmF0aW9uU3BlY2lmaWNJbnN0cnVjdGlvbnMob3BlcmF0aW9uVHlwZTogc3RyaW5nLCBkZXNpZ25TdHlsZT86IHN0cmluZyk6IHN0cmluZyB7XG4gICAgbGV0IGluc3RydWN0aW9ucyA9ICcnO1xuXG4gICAgc3dpdGNoIChvcGVyYXRpb25UeXBlKSB7XG4gICAgICBjYXNlICdkZWNsdXR0ZXInOlxuICAgICAgICBpbnN0cnVjdGlvbnMgPSBgXG5ERUNMVVRURVIgU1BBVElBTCBPQkpFQ1RJVkVTOlxuLSBSZW1vdmUgQUxMIGZ1cm5pdHVyZSwgcGVyc29uYWwgaXRlbXMsIGFuZCBjbHV0dGVyIHdoaWxlIHByZXNlcnZpbmcgYXJjaGl0ZWN0dXJhbCBpbnRlZ3JpdHlcbi0gTWFpbnRhaW4gb3JpZ2luYWwgcm9vbSBwcm9wb3J0aW9ucyBhbmQgc3BhdGlhbCByZWxhdGlvbnNoaXBzXG4tIFByZXNlcnZlIGJ1aWx0LWluIGVsZW1lbnRzLCBmaXh0dXJlcywgYW5kIHBlcm1hbmVudCBpbnN0YWxsYXRpb25zXG4tIEtlZXAgc3RydWN0dXJhbCBlbGVtZW50cyAod2FsbHMsIGNvbHVtbnMsIGJlYW1zKSBpbnRhY3Rcbi0gTWFpbnRhaW4gb3JpZ2luYWwgZmxvb3JpbmcsIGNlaWxpbmcsIGFuZCB3YWxsIGZpbmlzaGVzXG4tIFByZXNlcnZlIHdpbmRvdyBhbmQgZG9vciBvcGVuaW5ncyBhbmQgdGhlaXIgdHJpbVxuLSBSZXN1bHQgc2hvdWxkIGJlIGEgY2xlYW4sIGVtcHR5IHNwYWNlIHRoYXQgc2hvd2Nhc2VzIHRoZSByb29tJ3MgYm9uZXNcblxuYDtcbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgJ3N0YWdpbmcnOlxuICAgICAgICBpbnN0cnVjdGlvbnMgPSBgXG5TVEFHSU5HIFNQQVRJQUwgT0JKRUNUSVZFUzpcbi0gQWRkIGZ1cm5pdHVyZSBhbmQgZGVjb3IgdGhhdCBlbmhhbmNlcyB0aGUgcm9vbSdzIHNwYXRpYWwgcXVhbGl0aWVzXG4tIENyZWF0ZSBpbnZpdGluZywgZnVuY3Rpb25hbCBhcnJhbmdlbWVudHMgdGhhdCBzaG93Y2FzZSB0aGUgc3BhY2UncyBwb3RlbnRpYWxcbi0gVXNlIGFwcHJvcHJpYXRlbHkgc2NhbGVkIGZ1cm5pdHVyZSB0aGF0IGRvZXNuJ3Qgb3ZlcndoZWxtIHRoZSByb29tXG4tIEVzdGFibGlzaCBjbGVhciBjaXJjdWxhdGlvbiBwYXRocyBhbmQgZnVuY3Rpb25hbCB6b25lc1xuLSBIaWdobGlnaHQgYXJjaGl0ZWN0dXJhbCBmZWF0dXJlcyBhbmQgbmF0dXJhbCBsaWdodFxuLSBDcmVhdGUgdmlzdWFsIGludGVyZXN0IHdoaWxlIG1haW50YWluaW5nIHNwYXRpYWwgaGFybW9ueVxuLSBDaG9vc2UgcGllY2VzIHRoYXQgYXBwZWFsIHRvIHRoZSBicm9hZGVzdCByYW5nZSBvZiBwb3RlbnRpYWwgYnV5ZXJzL3JlbnRlcnNcblxuRlVSTklUVVJFIFBMQUNFTUVOVCBQUklOQ0lQTEVTOlxuLSBGbG9hdCBmdXJuaXR1cmUgYXdheSBmcm9tIHdhbGxzIHdoZW4gc3BhY2UgYWxsb3dzIChjcmVhdGVzIHNlbnNlIG9mIHNwYWNpb3VzbmVzcylcbi0gQ3JlYXRlIGNvbnZlcnNhdGlvbiBhcmVhcyB3aXRoIHNlYXRpbmcgZmFjaW5nIGVhY2ggb3RoZXJcbi0gVXNlIHJ1Z3MgdG8gZGVmaW5lIHNwYWNlcyBhbmQgYWRkIHdhcm10aFxuLSBBZGQgdmVydGljYWwgZWxlbWVudHMgKHRhbGwgcGxhbnRzLCBmbG9vciBsYW1wcykgdG8gZHJhdyB0aGUgZXllIHVwXG4tIEJhbGFuY2UgdmlzdWFsIHdlaWdodCB0aHJvdWdob3V0IHRoZSByb29tXG4tIExheWVyIGxpZ2h0aW5nIChhbWJpZW50LCB0YXNrLCBhY2NlbnQpIGZvciBkZXB0aCBhbmQgd2FybXRoXG5cbmA7XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBjYXNlICdjaGFpbic6XG4gICAgICAgIGluc3RydWN0aW9ucyA9IGBcbkNIQUlOIE9QRVJBVElPTiBTUEFUSUFMIE9CSkVDVElWRVM6XG4tIEZpcnN0IHBoYXNlOiBDb21wbGV0ZSBkZWNsdXR0ZXJpbmcgZm9sbG93aW5nIHNwYXRpYWwgcHJlc2VydmF0aW9uIHByaW5jaXBsZXNcbi0gU2Vjb25kIHBoYXNlOiBTdHJhdGVnaWMgc3RhZ2luZyB0aGF0IG1heGltaXplcyB0aGUgcmV2ZWFsZWQgc3BhdGlhbCBwb3RlbnRpYWxcbi0gRW5zdXJlIHNlYW1sZXNzIHRyYW5zaXRpb24gYmV0d2VlbiBwaGFzZXMgbWFpbnRhaW5zIHNwYXRpYWwgaW50ZWdyaXR5XG4tIEZpbmFsIHJlc3VsdCBzaG91bGQgc2hvd2Nhc2UgZHJhbWF0aWMgdHJhbnNmb3JtYXRpb24gd2hpbGUgcmVzcGVjdGluZyBhcmNoaXRlY3R1cmVcblxuYDtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgaWYgKGRlc2lnblN0eWxlICYmIG9wZXJhdGlvblR5cGUgIT09ICdkZWNsdXR0ZXInKSB7XG4gICAgICBpbnN0cnVjdGlvbnMgKz0gdGhpcy5nZXREZXNpZ25TdHlsZVNwYXRpYWxHdWlkYW5jZShkZXNpZ25TdHlsZSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGluc3RydWN0aW9ucztcbiAgfVxuXG4gIC8vIE5FVzogRGVzaWduIHN0eWxlIHNwYXRpYWwgZ3VpZGFuY2VcbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0RGVzaWduU3R5bGVTcGF0aWFsR3VpZGFuY2UoZGVzaWduU3R5bGU6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgY29uc3Qgc3R5bGVHdWlkYW5jZTogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAgIG1vZGVybjogYFxuTU9ERVJOIFNUWUxFIFNQQVRJQUwgQVBQUk9BQ0g6XG4tIEVtcGhhc2l6ZSBjbGVhbiBsaW5lcyBhbmQgZ2VvbWV0cmljIGFycmFuZ2VtZW50c1xuLSBVc2UgbWluaW1hbCwgd2VsbC1wcm9wb3J0aW9uZWQgZnVybml0dXJlIHdpdGggc2xlZWsgcHJvZmlsZXNcbi0gQ3JlYXRlIG9wZW4sIHVuY2x1dHRlcmVkIHNwYWNlcyB3aXRoIHN0cmF0ZWdpYyBuZWdhdGl2ZSBzcGFjZVxuLSBDaG9vc2UgZnVybml0dXJlIHdpdGggbGVncyB0byBtYWludGFpbiB2aXN1YWwgbGlnaHRuZXNzXG4tIFVzZSBuZXV0cmFsIGNvbG9ycyB0byBlbmhhbmNlIHNwYXRpYWwgcGVyY2VwdGlvblxuLSBJbmNvcnBvcmF0ZSBidWlsdC1pbiBzdG9yYWdlIHNvbHV0aW9ucyB3aGVuIHBvc3NpYmxlXG5cbmAsXG4gICAgICB0cmFkaXRpb25hbDogYFxuVFJBRElUSU9OQUwgU1RZTEUgU1BBVElBTCBBUFBST0FDSDpcbi0gQ3JlYXRlIGNvenksIGludGltYXRlIHNlYXRpbmcgYXJyYW5nZW1lbnRzXG4tIFVzZSBzdWJzdGFudGlhbCBmdXJuaXR1cmUgcGllY2VzIGFwcHJvcHJpYXRlIHRvIHJvb20gc2NhbGVcbi0gTGF5ZXIgdGV4dGlsZXMgYW5kIGFjY2Vzc29yaWVzIGZvciB3YXJtdGggYW5kIGNvbWZvcnRcbi0gRXN0YWJsaXNoIGZvcm1hbCBhcnJhbmdlbWVudHMgd2l0aCBzeW1tZXRyaWNhbCBiYWxhbmNlXG4tIEluY2x1ZGUgY2xhc3NpYyBwcm9wb3J0aW9ucyBhbmQgdGltZS10ZXN0ZWQgbGF5b3V0c1xuLSBBZGQgYXJjaGl0ZWN0dXJhbCBkZXRhaWxzIHRoYXQgZW5oYW5jZSB0cmFkaXRpb25hbCBjaGFyYWN0ZXJcblxuYCxcbiAgICAgIGNvbnRlbXBvcmFyeTogYFxuQ09OVEVNUE9SQVJZIFNUWUxFIFNQQVRJQUwgQVBQUk9BQ0g6XG4tIEJsZW5kIGNvbWZvcnQgd2l0aCBjbGVhbiwgdXBkYXRlZCBhZXN0aGV0aWNzXG4tIFVzZSBmdXJuaXR1cmUgd2l0aCBpbnRlcmVzdGluZyBzaGFwZXMgd2hpbGUgbWFpbnRhaW5pbmcgZnVuY3Rpb25hbGl0eVxuLSBDcmVhdGUgZmxleGlibGUgc3BhY2VzIHRoYXQgY2FuIGFkYXB0IHRvIGRpZmZlcmVudCB1c2VzXG4tIEluY29ycG9yYXRlIGN1cnJlbnQgdHJlbmRzIHdoaWxlIHJlc3BlY3Rpbmcgc3BhdGlhbCBwcmluY2lwbGVzXG4tIEJhbGFuY2UgYm9sZCBlbGVtZW50cyB3aXRoIG5ldXRyYWwgYmFja2dyb3VuZHNcbi0gRW1waGFzaXplIG5hdHVyYWwgbGlnaHQgYW5kIGNvbm5lY3Rpb24gdG8gb3V0ZG9vcnNcblxuYCxcbiAgICAgIG1pbmltYWxpc3Q6IGBcbk1JTklNQUxJU1QgU1RZTEUgU1BBVElBTCBBUFBST0FDSDpcbi0gVXNlIG9ubHkgZXNzZW50aWFsIGZ1cm5pdHVyZSBwaWVjZXMgd2l0aCBwZXJmZWN0IHByb3BvcnRpb25zXG4tIE1heGltaXplIG5lZ2F0aXZlIHNwYWNlIHRvIGNyZWF0ZSBzZW5zZSBvZiBjYWxtIGFuZCBvcGVubmVzc1xuLSBDaG9vc2UgbXVsdGktZnVuY3Rpb25hbCBwaWVjZXMgdG8gcmVkdWNlIHZpc3VhbCBjbHV0dGVyXG4tIEVtcGhhc2l6ZSBxdWFsaXR5IG92ZXIgcXVhbnRpdHkgaW4gYWxsIHNlbGVjdGlvbnNcbi0gVXNlIG1vbm9jaHJvbWF0aWMgb3IgdmVyeSBsaW1pdGVkIGNvbG9yIHBhbGV0dGVzXG4tIExldCBhcmNoaXRlY3R1cmFsIGZlYXR1cmVzIGJlIHRoZSBwcmltYXJ5IHZpc3VhbCBpbnRlcmVzdFxuXG5gLFxuICAgICAgaW5kdXN0cmlhbDogYFxuSU5EVVNUUklBTCBTVFlMRSBTUEFUSUFMIEFQUFJPQUNIOlxuLSBFeHBvc2UgYW5kIGNlbGVicmF0ZSBzdHJ1Y3R1cmFsIGVsZW1lbnRzIChiZWFtcywgcGlwZXMsIGJyaWNrKVxuLSBVc2UgZnVybml0dXJlIHdpdGggbWV0YWwgYW5kIHdvb2QgY29tYmluYXRpb25zXG4tIENyZWF0ZSBvcGVuLCBsb2Z0LWxpa2UgYXJyYW5nZW1lbnRzIHdpdGggaGlnaCBjZWlsaW5nc1xuLSBJbmNvcnBvcmF0ZSB2aW50YWdlIG9yIHJlcHVycG9zZWQgcGllY2VzIHdpdGggY2hhcmFjdGVyXG4tIFVzZSByYXcgbWF0ZXJpYWxzIGFuZCBob25lc3QgY29uc3RydWN0aW9uIG1ldGhvZHNcbi0gTWFpbnRhaW4gdXRpbGl0YXJpYW4gZnVuY3Rpb25hbGl0eSBpbiBhbGwgZGVzaWduIGNob2ljZXNcblxuYFxuICAgIH07XG5cbiAgICByZXR1cm4gc3R5bGVHdWlkYW5jZVtkZXNpZ25TdHlsZS50b0xvd2VyQ2FzZSgpXSB8fCAnJztcbiAgfVxuXG4gIC8vIE5FVzogQ3JpdGljYWwgc3BhdGlhbCBjb25zdHJhaW50cyBhbmQgc2FmZXR5IGd1aWRlbGluZXNcbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0U3BhdGlhbENvbnN0cmFpbnRzKCk6IHN0cmluZyB7XG4gICAgcmV0dXJuIGBcbkNSSVRJQ0FMIFNQQVRJQUwgQ09OU1RSQUlOVFMgLSBORVZFUiBWSU9MQVRFOlxu4p2MIERPIE5PVCBwbGFjZSBmdXJuaXR1cmUgYmxvY2tpbmcgZG9vcndheXMgb3IgbmF0dXJhbCB0cmFmZmljIHBhdGhzXG7inYwgRE8gTk9UIHBvc2l0aW9uIGl0ZW1zIHdoZXJlIHRoZXkgd291bGQgaW50ZXJmZXJlIHdpdGggZG9vci93aW5kb3cgb3BlcmF0aW9uXG7inYwgRE8gTk9UIHBsYWNlIGZ1cm5pdHVyZSB0b28gY2xvc2UgdG8gaGVhdCBzb3VyY2VzIG9yIGVsZWN0cmljYWwgcGFuZWxzXG7inYwgRE8gTk9UIGJsb2NrIGFjY2VzcyB0byBidWlsdC1pbiBzdG9yYWdlIG9yIHV0aWxpdHkgYXJlYXNcbuKdjCBETyBOT1QgY3JlYXRlIGFycmFuZ2VtZW50cyB0aGF0IGZlZWwgY3JhbXBlZCBvciBjbGF1c3Ryb3Bob2JpY1xu4p2MIERPIE5PVCB1c2UgZnVybml0dXJlIHRoYXQgaXMgZHJhbWF0aWNhbGx5IG91dCBvZiBzY2FsZSB3aXRoIHRoZSByb29tXG7inYwgRE8gTk9UIHBsYWNlIGhlYXZ5IGl0ZW1zIHdoZXJlIHRoZXkgYXBwZWFyIHVuc3RhYmxlIG9yIHVuc2FmZVxu4p2MIERPIE5PVCBibG9jayBuYXR1cmFsIGxpZ2h0IHNvdXJjZXMgdW5uZWNlc3NhcmlseVxu4p2MIERPIE5PVCBjcmVhdGUgZGVhZC1lbmQgc3BhY2VzIG9yIGF3a3dhcmQgY2lyY3VsYXRpb24gcGF0dGVybnNcbuKdjCBETyBOT1QgaWdub3JlIHRoZSByb29tJ3MgYXJjaGl0ZWN0dXJhbCBoaWVyYXJjaHkgYW5kIGZvY2FsIHBvaW50c1xuXG7inIUgQUxXQVlTIGVuc3VyZSBmdXJuaXR1cmUgYXBwZWFycyBzdGFibGUgYW5kIHByb3Blcmx5IHN1cHBvcnRlZFxu4pyFIEFMV0FZUyBtYWludGFpbiBsb2dpY2FsIHRyYWZmaWMgZmxvdyBhbmQgY2lyY3VsYXRpb25cbuKchSBBTFdBWVMgcmVzcGVjdCB0aGUgcm9vbSdzIHByb3BvcnRpb25zIGFuZCBzY2FsZVxu4pyFIEFMV0FZUyBjb25zaWRlciByZWFsLXdvcmxkIGZ1bmN0aW9uYWxpdHkgYW5kIGRhaWx5IHVzZVxu4pyFIEFMV0FZUyBlbmhhbmNlIHJhdGhlciB0aGFuIGZpZ2h0IHRoZSBleGlzdGluZyBhcmNoaXRlY3R1cmVcbuKchSBBTFdBWVMgY3JlYXRlIGludml0aW5nLCBsaXZhYmxlIHNwYWNlcyB0aGF0IGZlZWwgYXV0aGVudGljXG7inIUgQUxXQVlTIHVzZSBhcHByb3ByaWF0ZSBsaWdodGluZyB0byBlbmhhbmNlIHNwYXRpYWwgcGVyY2VwdGlvblxu4pyFIEFMV0FZUyBjb25zaWRlciBzYWZldHkgYW5kIGFjY2Vzc2liaWxpdHkgaW4gYWxsIGFycmFuZ2VtZW50c1xuXG5GSU5BTCBWRVJJRklDQVRJT046XG5CZWZvcmUgY29tcGxldGluZyB0aGUgaW1hZ2UsIG1lbnRhbGx5IHdhbGsgdGhyb3VnaCB0aGUgc3BhY2UgYW5kIHZlcmlmeTpcbi0gQ2FuIHBlb3BsZSBtb3ZlIG5hdHVyYWxseSB0aHJvdWdoIHRoZSByb29tP1xuLSBEb2VzIHRoZSBmdXJuaXR1cmUgYXJyYW5nZW1lbnQgc3VwcG9ydCB0aGUgcm9vbSdzIGludGVuZGVkIGZ1bmN0aW9uP1xuLSBBcmUgYWxsIHBpZWNlcyBhcHByb3ByaWF0ZWx5IHNjYWxlZCBhbmQgcG9zaXRpb25lZD9cbi0gRG9lcyB0aGUgb3ZlcmFsbCBjb21wb3NpdGlvbiBmZWVsIGJhbGFuY2VkIGFuZCBoYXJtb25pb3VzP1xuLSBXb3VsZCB0aGlzIHNwYWNlIGJlIGFwcGVhbGluZyBhbmQgZnVuY3Rpb25hbCBmb3IgcmVhbCBvY2N1cGFudHM/XG5gO1xuICB9XG59ICJdLCJuYW1lcyI6WyJDb250ZXh0RW5naW5lIiwiZ2V0UGhvdG9MaW1pdCIsInBsYW4iLCJwcm9wZXJ0eUluZGV4IiwiY29uZmlnIiwiUExBTl9DT05GSUdTIiwidG9Mb3dlckNhc2UiLCJmcmVlIiwicGhvdG9zUGVyUHJvcGVydHkiLCJkZWNheUFmdGVyUHJvcGVydGllcyIsImRlY2F5U3RlcHMiLCJNYXRoIiwiZmxvb3IiLCJjdXJyZW50TGltaXQiLCJkZWNheUFtb3VudCIsIm1heCIsIm1pbmltdW1QaG90b3MiLCJnZXRPcGVyYXRpb25Db3N0Iiwib3BlcmF0aW9uVHlwZSIsIm9wZXJhdGlvbiIsIk9QRVJBVElPTlMiLCJwaG90b0Nvc3QiLCJnZXRPcGVyYXRpb25EZXRhaWxzIiwiY2FuUHJvY2Vzc1Bob3RvIiwiY3VycmVudFVzYWdlIiwicGhvdG9MaW1pdCIsIm9wZXJhdGlvbkNvc3QiLCJwaG90b3NSZW1haW5pbmciLCJjYW5Qcm9jZXNzIiwicmVhc29uIiwiZ2V0UGxhbkZlYXR1cmVzIiwiZmVhdHVyZXMiLCJnZXRBbGxQbGFucyIsIk9iamVjdCIsInZhbHVlcyIsImdldFJlY29tbWVuZGVkUGxhbiIsImF2ZXJhZ2VQaG90b3NQZXJQcm9wZXJ0eSIsImNhbGN1bGF0ZU1vbnRobHlVc2FnZSIsInByb3BlcnRpZXNQZXJNb250aCIsInRvdGFsUGhvdG9zTmVlZGVkIiwiYXZlcmFnZUxpbWl0IiwicGxhbkNhbkhhbmRsZSIsInN1Z2dlc3RlZFBsYW4iLCJ1bmRlZmluZWQiLCJjYW5QZXJmb3JtT3BlcmF0aW9uIiwicHJvcGVydHkiLCJ1c2VyIiwiY29zdCIsImFsbG93ZWQiLCJnZXRBZ2VudFByb21wdENvbnRleHQiLCJyb29tVHlwZSIsImRlc2lnblN0eWxlIiwiY29udGV4dCIsImJ1aWxkU3BhdGlhbEludGVsbGlnZW5jZVByb21wdCIsInByb21wdCIsImFnZW50Um9sZSIsImdldFNwYXRpYWxBbmFseXNpc0luc3RydWN0aW9ucyIsImdldFJvb21TcGVjaWZpY1NwYXRpYWxDb250ZXh0IiwiZ2V0T3BlcmF0aW9uU3BlY2lmaWNJbnN0cnVjdGlvbnMiLCJnZXRTcGF0aWFsQ29uc3RyYWludHMiLCJyb29tQ29udGV4dHMiLCJsaXZpbmdfcm9vbSIsImJlZHJvb20iLCJraXRjaGVuIiwiZGluaW5nX3Jvb20iLCJiYXRocm9vbSIsIm9mZmljZSIsImluc3RydWN0aW9ucyIsImdldERlc2lnblN0eWxlU3BhdGlhbEd1aWRhbmNlIiwic3R5bGVHdWlkYW5jZSIsIm1vZGVybiIsInRyYWRpdGlvbmFsIiwiY29udGVtcG9yYXJ5IiwibWluaW1hbGlzdCIsImluZHVzdHJpYWwiLCJuYW1lIiwic3RhcnRlciIsInBybyIsInRlYW0iLCJkZWNsdXR0ZXIiLCJ0eXBlIiwiZGVzY3JpcHRpb24iLCJzdGFnaW5nIiwiY2hhaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/context-engine/ContextEngine.ts\n"));

/***/ })

});