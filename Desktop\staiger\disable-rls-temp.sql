-- TEMPORARY: Disable RLS for Development
-- Run this in your Supabase SQL Editor for easier development
-- RE-ENABLE RLS before going to production!

-- Temporarily disable RLS on tables that are causing issues
ALTER TABLE image_fingerprints D<PERSON><PERSON><PERSON> ROW LEVEL SECURITY;
ALTER TABLE usage_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE processing_jobs DISABLE ROW LEVEL SECURITY;

-- Keep RLS enabled on sensitive tables
-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE properties ENABLE ROW LEVEL SECURITY;

-- Note: Remember to re-enable RLS before production:
-- ALTER TABLE image_fingerprints ENABLE ROW LEVEL SECURITY;
-- <PERSON>TER TABLE usage_logs ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE processing_jobs ENABLE ROW LEVEL SECURITY; 