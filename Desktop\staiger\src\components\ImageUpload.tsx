"use client";

import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { Button } from './ui/Button';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  onImageSelect: (files: File[]) => void;
  maxFiles?: number;
  acceptedFileTypes?: string[];
  maxFileSize?: number;
}

export function ImageUpload({
  onImageSelect,
  maxFiles = 5,
  acceptedFileTypes = ['image/jpeg', 'image/png', 'image/webp'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
}: ImageUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const newFiles = [...uploadedFiles, ...acceptedFiles].slice(0, maxFiles);
      setUploadedFiles(newFiles);
      onImageSelect(newFiles);
      setIsDragActive(false);
    },
    [uploadedFiles, maxFiles, onImageSelect]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: maxFileSize,
    maxFiles,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
  });

  const removeFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(newFiles);
    onImageSelect(newFiles);
  };

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={cn(
          'relative rounded-lg border-2 border-dashed p-8 text-center transition-all duration-300 cursor-pointer',
          isDragActive
            ? 'border-royal-400 bg-royal-500/10 scale-105'
            : 'border-space-600 hover:border-royal-400 hover:bg-space-800/50'
        )}
      >
        <input {...getInputProps()} />
        <div className="space-y-4 animate-fade-up">
          <div className="mx-auto h-12 w-12 rounded-full bg-royal-500/10 flex items-center justify-center">
            <Upload className={cn(
              "h-6 w-6 transition-colors duration-200",
              isDragActive ? "text-royal-400" : "text-space-400"
            )} />
          </div>
          <div>
            <p className="text-lg font-medium text-foreground">
              {isDragActive ? 'Drop your images here' : 'Upload room photos'}
            </p>
            <p className="text-sm text-space-400">
              Drag & drop or click to select files
            </p>
            <p className="text-xs text-space-500 mt-2">
              Supports JPEG, PNG, WebP • Max {maxFiles} files • Up to {maxFileSize / (1024 * 1024)}MB each
            </p>
          </div>
        </div>
      </div>

      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-foreground">
            Uploaded Files ({uploadedFiles.length}/{maxFiles})
          </h4>
          <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
            {uploadedFiles.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="relative group rounded-lg border border-space-700 bg-space-800/30 p-3 animate-scale-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <ImageIcon className="h-8 w-8 text-royal-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-space-400">
                      {(file.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-500 text-white opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                  onClick={() => removeFile(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 