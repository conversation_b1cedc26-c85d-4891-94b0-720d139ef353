"use client";

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { User, Crown, CreditCard, BarChart3, Shield, LogOut, AlertTriangle } from 'lucide-react';
import { getUserProfile, updateUserProfile } from '@/lib/supabase';
import { useStore } from '@/store/useStore';

interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  plan: 'free' | 'starter' | 'pro' | 'team';
  created_at: string;
  last_login: string;
}

interface UsageStats {
  totalProperties: number;
  totalPhotosProcessed: number;
  remainingPhotos: number;
  planLimit: number;
  thisMonthUsage: number;
}

export default function SettingsPage() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [fullName, setFullName] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const supabase = createClientComponentClient();
  const { user, clearUser } = useStore();

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      // Load profile
      const userProfile = await getUserProfile(authUser.id);
      if (userProfile) {
        setProfile({
          id: userProfile.id,
          email: authUser.email || '',
          full_name: userProfile.full_name || '',
          plan: userProfile.plan || 'free',
          created_at: userProfile.created_at,
          last_login: userProfile.last_login || new Date().toISOString(),
        });
        setFullName(userProfile.full_name || '');
      }

      // Load usage stats
      await loadUsageStats(authUser.id);
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUsageStats = async (userId: string) => {
    try {
      // Get total properties
      const { count: totalProperties } = await supabase
        .from('properties')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      // Get total photos processed
      const { count: totalPhotosProcessed } = await supabase
        .from('processing_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('status', 'completed');

      // Get this month's usage
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { count: thisMonthUsage } = await supabase
        .from('processing_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('status', 'completed')
        .gte('created_at', startOfMonth.toISOString());

      // Calculate remaining photos (simplified)
      const planLimits = {
        free: 2,
        starter: 5,
        pro: 15,
        team: 50,
      };
      const planLimit = planLimits[profile?.plan || 'free'];
      const remainingPhotos = Math.max(0, planLimit - (thisMonthUsage || 0));

      setUsageStats({
        totalProperties: totalProperties || 0,
        totalPhotosProcessed: totalPhotosProcessed || 0,
        remainingPhotos,
        planLimit,
        thisMonthUsage: thisMonthUsage || 0,
      });
    } catch (error) {
      console.error('Error loading usage stats:', error);
    }
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!profile) return;

    try {
      setUpdating(true);
      setError(null);
      setSuccess(null);

      await updateUserProfile(profile.id, {
        full_name: fullName.trim(),
      });

      setProfile({ ...profile, full_name: fullName.trim() });
      setSuccess('Profile updated successfully!');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update profile');
    } finally {
      setUpdating(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      clearUser();
      window.location.href = '/login';
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleDeleteAccount = async () => {
    if (!profile) return;

    try {
      setUpdating(true);
      setError(null);

      // Note: In a real app, you'd want to handle this server-side
      // For now, we'll just sign out and show a message
      await supabase.auth.signOut();
      clearUser();
      window.location.href = '/login?message=account-deletion-requested';
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to delete account');
    } finally {
      setUpdating(false);
    }
  };

  const getPlanColor = (plan: string) => {
    const colors = {
      free: 'text-gray-400',
      starter: 'text-blue-400',
      pro: 'text-purple-400',
      team: 'text-gold-400',
    };
    return colors[plan as keyof typeof colors] || 'text-gray-400';
  };

  const getPlanIcon = (plan: string) => {
    if (plan === 'free') return <User className="h-5 w-5" />;
    return <Crown className="h-5 w-5" />;
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center space-y-4 animate-fade-up">
          <div className="h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent"></div>
          <p className="text-space-400">Loading settings...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-yellow-400 mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">Profile Not Found</h3>
            <p className="text-space-400 mb-6">Unable to load your profile information</p>
            <Button onClick={() => window.location.href = '/dashboard'}>
              Return to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8 animate-fade-up">
      <div>
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-space-400">Manage your account and preferences</p>
      </div>

      {/* Profile Information */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium">Profile Information</h3>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpdateProfile} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Email Address
              </label>
              <input
                type="email"
                value={profile.email}
                disabled
                className="w-full rounded-lg border border-space-700 bg-space-800/30 py-3 px-4 text-space-400 cursor-not-allowed"
              />
              <p className="text-xs text-space-500 mt-1">
                Email address cannot be changed
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Full Name
              </label>
              <input
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                placeholder="Enter your full name"
                className="w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground placeholder:text-space-400 transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20"
              />
            </div>

            {error && (
              <div className="animate-fade-in rounded-lg bg-red-500/10 border border-red-500/20 p-3">
                <p className="text-sm text-red-400">{error}</p>
              </div>
            )}

            {success && (
              <div className="animate-fade-in rounded-lg bg-green-500/10 border border-green-500/20 p-3">
                <p className="text-sm text-green-400">{success}</p>
              </div>
            )}

            <Button
              type="submit"
              variant="premium"
              isLoading={updating}
              disabled={fullName.trim() === profile.full_name}
            >
              Update Profile
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Current Plan */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium">Current Plan</h3>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`${getPlanColor(profile.plan)}`}>
                {getPlanIcon(profile.plan)}
              </div>
              <div>
                <p className="font-medium text-foreground capitalize">{profile.plan} Plan</p>
                <p className="text-sm text-space-400">
                  {usageStats?.planLimit} photos per property
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/pricing'}
              className="flex items-center space-x-2"
            >
              <CreditCard className="h-4 w-4" />
              <span>Upgrade Plan</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      {usageStats && (
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-royal-400" />
              <h3 className="text-lg font-medium">Usage Statistics</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-royal-400">
                  {usageStats.totalProperties}
                </p>
                <p className="text-sm text-space-400">Total Properties</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-400">
                  {usageStats.totalPhotosProcessed}
                </p>
                <p className="text-sm text-space-400">Photos Processed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-400">
                  {usageStats.remainingPhotos}
                </p>
                <p className="text-sm text-space-400">Photos Remaining</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-400">
                  {usageStats.thisMonthUsage}
                </p>
                <p className="text-sm text-space-400">This Month</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Account Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-royal-400" />
            <h3 className="text-lg font-medium">Account Actions</h3>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-foreground">Sign Out</p>
              <p className="text-sm text-space-400">Sign out of your account</p>
            </div>
            <Button
              variant="outline"
              onClick={handleSignOut}
              className="flex items-center space-x-2"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign Out</span>
            </Button>
          </div>

          <div className="border-t border-space-700 pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-red-400">Delete Account</p>
                <p className="text-sm text-space-400">Permanently delete your account and all data</p>
              </div>
              <Button
                variant="ghost"
                onClick={() => setShowDeleteConfirm(true)}
                className="text-red-400 hover:bg-red-500/10 hover:text-red-300"
              >
                Delete Account
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-400" />
                <h3 className="text-lg font-medium text-red-400">Delete Account</h3>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-space-300">
                Are you sure you want to delete your account? This action cannot be undone.
              </p>
              <p className="text-sm text-space-400">
                All your properties, photos, and processing history will be permanently deleted.
              </p>
              <div className="flex space-x-4">
                <Button
                  variant="ghost"
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleDeleteAccount}
                  isLoading={updating}
                  className="flex-1 bg-red-500/10 text-red-400 hover:bg-red-500/20"
                >
                  Delete Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Account Info */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-space-400">
            <span>Account created: {new Date(profile.created_at).toLocaleDateString()}</span>
            <span>Last login: {new Date(profile.last_login).toLocaleDateString()}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 