import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Staiger AI - Virtual Staging Platform",
  description: "Transform your real estate photos with AI-powered virtual staging and decluttering",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-space-900 text-foreground antialiased`}>
        {children}
      </body>
    </html>
  );
}
