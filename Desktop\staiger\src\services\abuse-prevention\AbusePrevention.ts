import { supabase } from '@/lib/supabase';

interface RateLimitInfo {
  allowed: boolean;
  remaining: number;
  resetTime: number; // minutes until reset
  reason?: string;
}

interface ImageFingerprint {
  hash: string;
  userId: string;
  propertyId: string;
  imageUrl: string;
  createdAt: string;
}

export class AbusePrevention {
  private static readonly RATE_LIMITS = {
    // Properties per hour
    PROPERTIES_PER_HOUR: 3,
    // Images per hour
    IMAGES_PER_HOUR: 10,
    // Images per day
    IMAGES_PER_DAY: 50,
    // Cooldown period after rate limit hit (minutes)
    COOLDOWN_PERIOD: 60,
  };

  private static readonly ABUSE_THRESHOLDS = {
    // Duplicate image threshold
    DUPLICATE_THRESHOLD: 3,
    // Suspicious activity threshold
    SUSPICIOUS_ACTIVITY_THRESHOLD: 10,
    // Address validation strictness
    MIN_ADDRESS_LENGTH: 10,
  };



  /**
   * Check if user has exceeded rate limits
   */
  static async checkRateLimit(userId: string): Promise<RateLimitInfo> {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Check properties created in last hour
      const { count: propertiesLastHour } = await supabase
        .from('properties')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('created_at', oneHourAgo.toISOString());

      if ((propertiesLastHour || 0) >= this.RATE_LIMITS.PROPERTIES_PER_HOUR) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: this.RATE_LIMITS.COOLDOWN_PERIOD,
          reason: 'Property creation rate limit exceeded'
        };
      }

      // Check processing jobs in last hour
      const { count: jobsLastHour } = await supabase
        .from('processing_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('created_at', oneHourAgo.toISOString());

      if ((jobsLastHour || 0) >= this.RATE_LIMITS.IMAGES_PER_HOUR) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: this.RATE_LIMITS.COOLDOWN_PERIOD,
          reason: 'Image processing rate limit exceeded'
        };
      }

      // Check processing jobs in last day
      const { count: jobsLastDay } = await supabase
        .from('processing_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('created_at', oneDayAgo.toISOString());

      if ((jobsLastDay || 0) >= this.RATE_LIMITS.IMAGES_PER_DAY) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: 24 * 60, // 24 hours
          reason: 'Daily image processing limit exceeded'
        };
      }

      return {
        allowed: true,
        remaining: this.RATE_LIMITS.IMAGES_PER_HOUR - (jobsLastHour || 0),
        resetTime: 0
      };

    } catch (error) {
      console.error('Rate limit check failed:', error);
      return {
        allowed: false,
        remaining: 0,
        resetTime: this.RATE_LIMITS.COOLDOWN_PERIOD,
        reason: 'Rate limit check failed'
      };
    }
  }



  /**
   * Generate perceptual hash for image fingerprinting
   */
  static async generateImageHash(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Resize to 8x8 for perceptual hashing
        canvas.width = 8;
        canvas.height = 8;
        ctx?.drawImage(img, 0, 0, 8, 8);

        // Get image data and create hash
        const imageData = ctx?.getImageData(0, 0, 8, 8);
        if (!imageData) {
          reject(new Error('Failed to get image data'));
          return;
        }

        let hash = '';
        const data = imageData.data;
        
        // Simple perceptual hash based on grayscale values
        for (let i = 0; i < data.length; i += 4) {
          const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
          hash += gray > 128 ? '1' : '0';
        }

        resolve(hash);
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Check if image is a duplicate based on perceptual hash
   */
  static async checkImageFingerprint(
    userId: string,
    propertyId: string,
    file: File
  ): Promise<boolean> {
    try {
      const hash = await this.generateImageHash(file);

      // Check for exact hash match
      const { data: exactMatch } = await supabase
        .from('image_fingerprints')
        .select('*')
        .eq('user_id', userId)
        .eq('image_hash', hash)
        .single();

      if (exactMatch) {
        return true; // Exact duplicate found
      }

      // Check for similar hashes (Hamming distance <= 2)
      const { data: allHashes } = await supabase
        .from('image_fingerprints')
        .select('image_hash')
        .eq('user_id', userId);

      if (allHashes) {
        for (const record of allHashes) {
          const hammingDistance = this.calculateHammingDistance(hash, record.image_hash);
          if (hammingDistance <= 2) {
            return true; // Similar image found
          }
        }
      }

      // Store new hash
      await supabase
        .from('image_fingerprints')
        .insert({
          user_id: userId,
          property_id: propertyId,
          image_hash: hash,
          image_url: URL.createObjectURL(file),
        });

      return false; // Not a duplicate

    } catch (error) {
      console.error('Image fingerprint check failed:', error);
      return false; // Allow processing if check fails
    }
  }

  /**
   * Calculate Hamming distance between two hash strings
   */
  static calculateHammingDistance(hash1: string, hash2: string): number {
    if (hash1.length !== hash2.length) return Infinity;
    
    let distance = 0;
    for (let i = 0; i < hash1.length; i++) {
      if (hash1[i] !== hash2[i]) distance++;
    }
    return distance;
  }

  /**
   * Verify image file integrity and format
   */
  static async verifyImageIntegrity(file: File): Promise<boolean> {
    try {
      // Check file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        return false;
      }

      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        return false;
      }

      // Check minimum size (prevent 1x1 pixel abuse)
      const minSize = 1024; // 1KB minimum
      if (file.size < minSize) {
        return false;
      }

      // Verify it's actually an image by loading it
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          // Check minimum dimensions
          const minDimension = 100;
          resolve(img.width >= minDimension && img.height >= minDimension);
        };
        img.onerror = () => resolve(false);
        img.src = URL.createObjectURL(file);
      });

    } catch (error) {
      console.error('Image verification failed:', error);
      return false;
    }
  }

  /**
   * Validate property address to prevent fake entries
   */
  static validateAddress(address: string): { isValid: boolean; reason?: string } {
    const trimmedAddress = address.trim();

    // Check minimum length
    if (trimmedAddress.length < this.ABUSE_THRESHOLDS.MIN_ADDRESS_LENGTH) {
      return { isValid: false, reason: 'Address too short' };
    }

    // Check for basic address pattern (number + street name)
    const addressPattern = /^\d+\s+[A-Za-z0-9\s,.-]+$/;
    if (!addressPattern.test(trimmedAddress)) {
      return { isValid: false, reason: 'Invalid address format' };
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /test/i,
      /fake/i,
      /dummy/i,
      /example/i,
      /123\s*main/i,
      /^\d+\s*[a-z]\s*$/, // Just number + single letter
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(trimmedAddress)) {
        return { isValid: false, reason: 'Suspicious address pattern detected' };
      }
    }

    return { isValid: true };
  }

  /**
   * Check for duplicate addresses to prevent abuse
   */
  static async checkDuplicateAddress(
    userId: string,
    address: string
  ): Promise<boolean> {
    try {
      const { data: existingProperty } = await supabase
        .from('properties')
        .select('id')
        .eq('user_id', userId)
        .ilike('address', address.trim())
        .single();

      return !!existingProperty;
    } catch (error) {
      // If error, assume no duplicate (single() throws if no match)
      return false;
    }
  }

  /**
   * Log suspicious activity for monitoring
   */
  static async logSuspiciousActivity(
    userId: string,
    activity: string,
    details: any
  ): Promise<void> {
    try {
      await supabase
        .from('usage_logs')
        .insert({
          user_id: userId,
          property_id: null,
          job_id: null,
          photos_consumed: 0,
          operation_type: `SUSPICIOUS_${activity}`,
          metadata: details,
        });
    } catch (error) {
      console.error('Failed to log suspicious activity:', error);
    }
  }

  /**
   * Log processing activity for analytics
   */
  static async logProcessingActivity(
    userId: string,
    propertyId: string,
    imageCount: number,
    operationType: string
  ): Promise<void> {
    try {
      await supabase
        .from('usage_logs')
        .insert({
          user_id: userId,
          property_id: propertyId,
          job_id: null,
          photos_consumed: imageCount,
          operation_type: `ACTIVITY_${operationType.toUpperCase()}`,
        });
    } catch (error) {
      console.error('Failed to log processing activity:', error);
    }
  }

  /**
   * Check user's overall abuse score
   */
  static async calculateAbuseScore(userId: string): Promise<number> {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Count suspicious activities
      const { count: suspiciousCount } = await supabase
        .from('usage_logs')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .like('operation_type', 'SUSPICIOUS_%')
        .gte('created_at', oneDayAgo.toISOString());

      // Count duplicate attempts
      const { count: duplicateCount } = await supabase
        .from('image_fingerprints')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('created_at', oneDayAgo.toISOString());

      // Calculate score (0-100, higher = more suspicious)
      const suspiciousScore = Math.min((suspiciousCount || 0) * 10, 50);
      const duplicateScore = Math.min((duplicateCount || 0) * 5, 30);
      
      return suspiciousScore + duplicateScore;

    } catch (error) {
      console.error('Abuse score calculation failed:', error);
      return 0;
    }
  }

  /**
   * Check if user should be temporarily restricted
   */
  static async shouldRestrictUser(userId: string): Promise<boolean> {
    const abuseScore = await this.calculateAbuseScore(userId);
    return abuseScore >= this.ABUSE_THRESHOLDS.SUSPICIOUS_ACTIVITY_THRESHOLD;
  }

  /**
   * Comprehensive abuse check before processing
   */
  static async performAbuseCheck(
    userId: string,
    propertyId: string,
    files: File[]
  ): Promise<{ allowed: boolean; reason?: string }> {
    try {
      // Check if user is restricted
      const isRestricted = await this.shouldRestrictUser(userId);
      if (isRestricted) {
        return { allowed: false, reason: 'Account temporarily restricted due to suspicious activity' };
      }

      // Check rate limits
      const rateLimitInfo = await this.checkRateLimit(userId);
      if (!rateLimitInfo.allowed) {
        return { allowed: false, reason: rateLimitInfo.reason };
      }

      // Check each file
      for (const file of files) {
        // Verify image integrity
        const isValidImage = await this.verifyImageIntegrity(file);
        if (!isValidImage) {
          return { allowed: false, reason: `Invalid image file: ${file.name}` };
        }

        // Check for duplicates
        const isDuplicate = await this.checkImageFingerprint(userId, propertyId, file);
        if (isDuplicate) {
          await this.logSuspiciousActivity(userId, 'DUPLICATE_IMAGE', { fileName: file.name });
          return { allowed: false, reason: `Duplicate image detected: ${file.name}` };
        }
      }

      return { allowed: true };

    } catch (error) {
      console.error('Abuse check failed:', error);
      return { allowed: false, reason: 'Security check failed' };
    }
  }
} 