graph TD
    subgraph "Frontend"
        UI["UI Components"]
        Store["Zustand Store"]
        Hooks["Custom Hooks"]
        API["API Client"]
    end

    subgraph "Backend API Routes"
        Auth["Auth Middleware"]
        Properties["Properties API"]
        Process["Image Processing API"]
    end

    subgraph "Core Services"
        CE["Context Engine"]
        AP["Abuse Prevention"]
        AI["AI Service"]
    end

    subgraph "Database"
        Users["Users Table"]
        Props["Properties Table"]
        Jobs["Processing Jobs Table"]
    end

    subgraph "Storage"
        Images["Image Storage"]
    end

    UI --> Store
    UI --> Hooks
    Hooks --> API
    API --> Auth
    Auth --> Properties
    Auth --> Process
    Properties --> CE
    Properties --> AP
    Process --> CE
    Process --> AP
    Process --> AI
    Properties --> Users
    Properties --> Props
    Process --> Jobs
    Process --> Images
    AI --> Images

    graph TD
    A["User Uploads Image"] --> B["Select AI Agent Operation"]
    B --> C{"Agent Type?"}
    
    C -->|Declutter| D["AI Declutter Agent"]
    C -->|Staging| E["AI Staging Agent"] 
    C -->|Chain| F["AI Chain Agent"]
    
    D --> D1["Agent Context:<br/>- Specialized clutter removal<br/>- Preserve architecture<br/>- Room type awareness"]
    E --> E1["Agent Context:<br/>- Expert interior designer<br/>- Room-specific furniture<br/>- Style preferences"]
    F --> F1["Agent Context:<br/>- Master coordinator<br/>- Declutter THEN Stage<br/>- Complete transformation"]
    
    D1 --> G["Gemini 2.0 Flash<br/>Experimental"]
    E1 --> G
    F1 --> G
    
    G --> H["Intelligent Prompt:<br/>'You are a [Agent Role]<br/>Working on [Room Type]<br/>Style: [Design Style]<br/>[Specific Instructions]'"]
    
    H --> I["AI Image Generation"]
    I --> J["Store in Supabase"]
    J --> K["Add Staiger Watermark"]
    K --> L["Return to User"]
    
    M["Context Engine"] --> N["Photo Limits:<br/>Free: 2 photos<br/>Starter: 5 photos<br/>Pro: 15 photos<br/>Team: 50 photos"]
    N --> O["Track Usage:<br/>Declutter: 1 photo<br/>Staging: 1 photo<br/>Chain: 2 photos"]