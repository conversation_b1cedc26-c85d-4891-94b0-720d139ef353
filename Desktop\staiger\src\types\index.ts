export type DesignStyle = 'modern' | 'cozy' | 'minimal' | 'traditional' | 'industrial';

export type RoomType = 'living' | 'bedroom' | 'kitchen' | 'bathroom' | 'dining' | 'office';

export type PlanTier = 'free' | 'starter' | 'pro' | 'team';

export type ContextOperation = 'declutter' | 'stage' | 'chain';

export interface Property {
  id: string;
  address: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  contextUsed: number;
  contextLimit: number;
  imageHashes: string[];
}

export interface ProcessingJob {
  id: string;
  propertyId: string;
  operation: ContextOperation;
  roomType: RoomType;
  designStyle?: DesignStyle;
  inputImageUrl: string;
  outputImageUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  contextCost: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  email: string;
  planTier: PlanTier;
  propertiesProcessed: number;
  totalContextUsed: number;
}

export interface ContextLimits {
  [key in PlanTier]: {
    start: number;
    decay: number;
    interval: number;
    floor: number;
  };
} 