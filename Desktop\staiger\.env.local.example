# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI Service Configuration - KEEP THIS SECRET!
GEMINI_API_KEY=AIzaSyD5zx6oKoj6sVQSwHSiIWI9tvnfgY7F8RU

# Image Processing Configuration
MAX_IMAGE_SIZE_MB=10
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,webp

# Storage Configuration
STORAGE_BUCKET=property-images
MAX_STORAGE_PER_USER_GB=5

# Rate Limiting Configuration
RATE_LIMIT_IMAGES_PER_HOUR=10
RATE_LIMIT_IMAGES_PER_DAY=50
RATE_LIMIT_PROPERTIES_PER_HOUR=3

# Payment Configuration (Stripe)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Security Configuration
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# Feature Flags
ENABLE_WATERMARKING=true
ENABLE_RATE_LIMITING=true
ENABLE_ABUSE_PREVENTION=true
ENABLE_IMAGE_FINGERPRINTING=true

# Monitoring & Analytics
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_ga_id

# Development Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Database Configuration (if using external DB)
DATABASE_URL=your_database_url

# Redis Configuration (for rate limiting)
REDIS_URL=your_redis_url

# Email Configuration
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
FROM_EMAIL=<EMAIL>

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Watermark Configuration
WATERMARK_TEXT="Staiger AI Virtualization"
WATERMARK_POSITION=bottom-left
WATERMARK_OPACITY=0.7

# API Configuration
API_TIMEOUT_MS=30000
MAX_CONCURRENT_REQUESTS=5

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_FREQUENCY_HOURS=24
BACKUP_RETENTION_DAYS=30 