"use client";

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Building2, Plus, MapPin, Calendar, Camera, Trash2 } from 'lucide-react';
import { getUserProperties, createProperty, getUserProfile } from '@/lib/supabase';
import { ContextEngine } from '@/services/context-engine/ContextEngine';
import { useStore } from '@/store/useStore';

interface Property {
  id: string;
  address: string;
  property_type: string;
  photos_used: number;
  photo_limit: number;
  property_index: number;
  created_at: string;
}

export default function PropertiesPage() {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newAddress, setNewAddress] = useState('');
  const [newPropertyType, setNewPropertyType] = useState('residential');
  const [addingProperty, setAddingProperty] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient();
  const { user, setSelectedProperty } = useStore();

  useEffect(() => {
    loadProperties();
  }, []);

  const loadProperties = async () => {
    try {
      setLoading(true);
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      const userProperties = await getUserProperties(authUser.id);
      setProperties(userProperties);
    } catch (error) {
      console.error('Error loading properties:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddProperty = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newAddress.trim()) return;

    try {
      setAddingProperty(true);
      setError(null);

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      // Ensure user profile exists, create if not
      let userProfile = await getUserProfile(authUser.id);
      if (!userProfile) {
        // Create user profile if it doesn't exist
        const { error: createError } = await supabase
          .from('users')
          .insert({
            id: authUser.id,
            email: authUser.email || '',
            full_name: authUser.user_metadata?.full_name || '',
            plan: 'free',
            created_at: new Date().toISOString(),
            last_login: new Date().toISOString(),
          });

        if (createError) {
          throw new Error('Failed to create user profile');
        }

        // Fetch the newly created profile
        userProfile = await getUserProfile(authUser.id);
      }

      const plan = userProfile?.plan || 'free';

      // Calculate property index and photo limit
      const propertyIndex = properties.length;
      const photoLimit = ContextEngine.getPhotoLimit(plan, propertyIndex);

      // Validate address (basic validation)
      if (!isValidAddress(newAddress)) {
        throw new Error('Please enter a valid street address');
      }

      // Check for duplicate addresses (abuse prevention)
      const duplicateAddress = properties.find(
        p => p.address.toLowerCase() === newAddress.toLowerCase()
      );
      if (duplicateAddress) {
        throw new Error('This address has already been added');
      }

      // Rate limiting check - max 3 properties per hour
      const recentProperties = properties.filter(p => {
        const createdAt = new Date(p.created_at);
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        return createdAt > oneHourAgo;
      });

      if (recentProperties.length >= 3) {
        throw new Error('Rate limit exceeded. Please wait before adding more properties.');
      }

      const newProperty = await createProperty(
        authUser.id,
        newAddress,
        newPropertyType,
        photoLimit,
        propertyIndex
      );

      if (newProperty) {
        setProperties([newProperty, ...properties]);
        setNewAddress('');
        setNewPropertyType('residential');
        setShowAddForm(false);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to add property');
    } finally {
      setAddingProperty(false);
    }
  };

  const isValidAddress = (address: string): boolean => {
    // Basic address validation
    const addressRegex = /^\d+\s+[A-Za-z0-9\s,.-]+$/;
    return addressRegex.test(address.trim()) && address.length >= 10;
  };

  const handleSelectProperty = (property: Property) => {
    setSelectedProperty({
      id: property.id,
      address: property.address,
      type: property.property_type,
      photosUsed: property.photos_used,
      photoLimit: property.photo_limit,
      createdAt: property.created_at,
    });
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center space-y-4 animate-fade-up">
          <div className="h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent"></div>
          <p className="text-space-400">Loading your properties...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 animate-fade-up">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Properties</h1>
          <p className="text-space-400">Manage your real estate properties</p>
        </div>
        <Button
          onClick={() => setShowAddForm(true)}
          variant="premium"
          className="flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Property</span>
        </Button>
      </div>

      {showAddForm && (
        <Card className="animate-fade-up">
          <CardHeader>
            <h3 className="text-lg font-medium">Add New Property</h3>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddProperty} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Property Address *
                </label>
                <input
                  type="text"
                  value={newAddress}
                  onChange={(e) => setNewAddress(e.target.value)}
                  placeholder="123 Main Street, City, State"
                  className="w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground placeholder:text-space-400 transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20"
                  required
                />
                <p className="text-xs text-space-500 mt-1">
                  Enter a valid street address (e.g., 123 Main St, Anytown, CA)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Property Type
                </label>
                <select
                  value={newPropertyType}
                  onChange={(e) => setNewPropertyType(e.target.value)}
                  className="w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20"
                >
                  <option value="residential">Residential</option>
                  <option value="commercial">Commercial</option>
                  <option value="condo">Condo</option>
                  <option value="townhouse">Townhouse</option>
                  <option value="apartment">Apartment</option>
                </select>
              </div>

              {error && (
                <div className="animate-fade-in rounded-lg bg-red-500/10 border border-red-500/20 p-3">
                  <p className="text-sm text-red-400">{error}</p>
                </div>
              )}

              <div className="flex items-center space-x-4">
                <Button
                  type="submit"
                  variant="premium"
                  isLoading={addingProperty}
                  disabled={!newAddress.trim()}
                >
                  Add Property
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => {
                    setShowAddForm(false);
                    setError(null);
                    setNewAddress('');
                    setNewPropertyType('residential');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {properties.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="h-16 w-16 mx-auto rounded-full bg-royal-500/10 flex items-center justify-center mb-4">
              <Building2 className="h-8 w-8 text-royal-400" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">No properties yet</h3>
            <p className="text-space-400 mb-6">
              Add your first property to start transforming photos with AI
            </p>
            <Button
              onClick={() => setShowAddForm(true)}
              variant="premium"
            >
              Add Your First Property
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {properties.map((property, index) => (
            <Card
              key={property.id}
              className="cursor-pointer hover:scale-105 transition-all duration-200"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => handleSelectProperty(property)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-5 w-5 text-royal-400" />
                    <span className="font-medium text-foreground capitalize">
                      {property.property_type}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-space-400">
                    <Camera className="h-3 w-3" />
                    <span>{property.photos_used}/{property.photo_limit}</span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-space-400 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-space-300 line-clamp-2">{property.address}</p>
                </div>
                
                <div className="flex items-center justify-between text-xs text-space-400">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3" />
                    <span>{new Date(property.created_at).toLocaleDateString()}</span>
                  </div>
                  <span>Property #{property.property_index + 1}</span>
                </div>

                {/* Photo usage bar */}
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-space-400">Photo Usage</span>
                    <span className={property.photos_used >= property.photo_limit ? 'text-red-400' : 'text-space-400'}>
                      {property.photos_used}/{property.photo_limit}
                    </span>
                  </div>
                  <div className="w-full h-2 bg-space-700 rounded-full overflow-hidden">
                    <div
                      className={`h-full transition-all duration-500 rounded-full ${
                        property.photos_used >= property.photo_limit
                          ? 'bg-gradient-to-r from-red-500 to-red-600'
                          : 'bg-gradient-to-r from-royal-500 to-purple-600'
                      }`}
                      style={{ width: `${Math.min((property.photos_used / property.photo_limit) * 100, 100)}%` }}
                    />
                  </div>
                </div>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelectProperty(property);
                    // Navigate to processing page
                    window.location.href = '/dashboard/process';
                  }}
                >
                  Process Photos
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
} 