import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { ContextOperation, PlanTier } from '@/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

export function getPlanDetails(tier: PlanTier): {
  name: string;
  price: number;
  features: string[];
} {
  const plans = {
    free: {
      name: 'Free',
      price: 0,
      features: ['2 context per property', 'Basic room types', 'Standard quality'],
    },
    starter: {
      name: 'Starter',
      price: 10,
      features: [
        '5 context per property',
        'All room types',
        'Premium quality',
        'Priority support',
      ],
    },
    pro: {
      name: 'Pro',
      price: 30,
      features: [
        '8 context per property',
        'All room types',
        'Ultra quality',
        'Priority support',
        'API access',
      ],
    },
    team: {
      name: 'Team',
      price: 99,
      features: [
        '10 context per property',
        'All features',
        'Ultra quality',
        'Dedicated support',
        'API access',
        'Custom branding',
      ],
    },
  };

  return plans[tier];
}

export function getOperationLabel(operation: ContextOperation): string {
  const labels = {
    declutter: 'De-Clutter Room',
    stage: 'Stage Room',
    chain: 'De-Clutter & Stage',
  };

  return labels[operation];
}

export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function generatePlaceholderPrompt(roomType: string): string {
  const prompts = {
    living: 'Modern living room with comfortable seating...',
    bedroom: 'Cozy bedroom with natural light...',
    kitchen: 'Contemporary kitchen with clean lines...',
    bathroom: 'Elegant bathroom with modern fixtures...',
    dining: 'Welcoming dining space with ambient lighting...',
    office: 'Professional home office setup...',
  } as const;

  return prompts[roomType as keyof typeof prompts] || 'Transform this space...';
}

export function isValidImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
  return validTypes.includes(file.type);
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  return String(error);
} 