"use client";

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ImageUpload } from '@/components/ImageUpload';
import { Camera, Wand2, Sparkles, AlertTriangle, CheckCircle, Clock, X, Eye } from 'lucide-react';
import { ContextEngine } from '@/services/context-engine/ContextEngine';
import { useImageProcessing } from '@/hooks/useImageProcessing';
import { useStore } from '@/store/useStore';

interface ProcessingJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  operationType: 'declutter' | 'staging' | 'chain';
  inputImageUrl: string;
  outputImageUrl?: string;
  photoCost: number;
  createdAt: string;
  error?: string;
}

export default function ProcessPage() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [operationType, setOperationType] = useState<'declutter' | 'staging' | 'chain'>('staging');
  const [roomType, setRoomType] = useState('living_room');
  const [designStyle, setDesignStyle] = useState('modern');
  const [jobs, setJobs] = useState<ProcessingJob[]>([]);
  const [viewingImage, setViewingImage] = useState<string | null>(null);
  
  const { selectedProperty, photosUsed, photoLimit, updatePhotoUsage } = useStore();
  const { 
    processQueue, 
    isProcessing, 
    error, 
    queue, 
    currentProcessingIndex,
    clearQueue,
    clearError 
  } = useImageProcessing();
  const supabase = createClientComponentClient();

  const getOperationCost = (operation: string): number => {
    const costs = {
      declutter: 1,
      staging: 1,
      chain: 2, // declutter + staging
    };
    return costs[operation as keyof typeof costs] || 1;
  };

  const canProcessPhotos = (): { canProcess: boolean; reason?: string } => {
    if (!selectedProperty) {
      return { canProcess: false, reason: 'Please select a property first' };
    }

    if (selectedFiles.length === 0) {
      return { canProcess: false, reason: 'Please upload at least one photo' };
    }

    const totalCost = selectedFiles.length * getOperationCost(operationType);
    const remainingPhotos = photoLimit - photosUsed;

    if (totalCost > remainingPhotos) {
      return { 
        canProcess: false, 
        reason: `Not enough photos remaining. Need ${totalCost}, have ${remainingPhotos}` 
      };
    }

    return { canProcess: true };
  };

  const handleProcessPhotos = async () => {
    const validation = canProcessPhotos();
    if (!validation.canProcess) {
      return;
    }

    try {
      clearError();
      clearQueue();

      const { data: { user } } = await supabase.auth.getUser();
      if (!user || !selectedProperty) return;

      // Process all images using the queue system
      const results = await processQueue({
        propertyId: selectedProperty.id,
        operation: operationType,
        roomType,
        designStyle,
        images: selectedFiles,
      });

      // Convert successful results to jobs
      const newJobs: ProcessingJob[] = results.map((result, index) => ({
        id: result.jobId,
        status: 'completed',
        operationType,
        inputImageUrl: URL.createObjectURL(selectedFiles[index]),
        outputImageUrl: result.outputImageUrl,
        photoCost: result.photosConsumed,
        createdAt: new Date().toISOString(),
      }));

      // Add failed jobs from queue
      const failedJobs: ProcessingJob[] = queue
        .filter(item => item.status === 'failed')
        .map((item, index) => ({
          id: item.id,
          status: 'failed',
          operationType,
          inputImageUrl: URL.createObjectURL(item.file),
          photoCost: getOperationCost(operationType),
          createdAt: new Date().toISOString(),
          error: item.error,
        }));

      // Update usage with successful results only
      const totalPhotosUsed = results.reduce((sum, result) => sum + result.photosConsumed, 0);
      updatePhotoUsage(photosUsed + totalPhotosUsed);
      
      // Add all jobs to the list
      setJobs([...newJobs, ...failedJobs, ...jobs]);
      setSelectedFiles([]);

      // Log successful processing
      console.log(`Successfully processed ${results.length}/${selectedFiles.length} images for user ${user.id}`);

    } catch (error) {
      console.error('Processing error:', error);
    }
  };

  // Image viewer modal
  const ImageViewer = ({ imageUrl, onClose }: { imageUrl: string; onClose: () => void }) => (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50" onClick={onClose}>
      <div className="relative max-w-4xl max-h-[90vh] p-4">
        <button
          onClick={onClose}
          className="absolute -top-2 -right-2 bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
        >
          <X className="h-5 w-5 text-white" />
        </button>
        <img
          src={imageUrl}
          alt="Full size view"
          className="max-w-full max-h-full object-contain rounded-lg"
          onClick={(e) => e.stopPropagation()}
        />
      </div>
    </div>
  );

  if (!selectedProperty) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-yellow-400 mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No Property Selected</h3>
            <p className="text-space-400 mb-6">
              Please select a property before processing photos
            </p>
            <Button onClick={() => window.location.href = '/dashboard/properties'}>
              Select Property
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const remainingPhotos = photoLimit - photosUsed;
  const operationCost = getOperationCost(operationType);
  const maxFiles = Math.floor(remainingPhotos / operationCost);

  return (
    <div className="space-y-8 animate-fade-up">
      <div>
        <h1 className="text-3xl font-bold">Process Photos</h1>
        <p className="text-space-400">Transform your property photos with AI</p>
      </div>

      {/* Property Info */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium">Selected Property</h3>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-foreground">{selectedProperty.address}</p>
              <p className="text-sm text-space-400 capitalize">{selectedProperty.type}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-space-400">Photos Remaining</p>
              <p className="text-lg font-bold text-royal-400">
                {remainingPhotos}/{photoLimit}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Section */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium">Upload Photos</h3>
          <p className="text-sm text-space-400">
            Upload up to {maxFiles} photos ({operationCost} photo{operationCost > 1 ? 's' : ''} per {operationType})
          </p>
        </CardHeader>
        <CardContent>
          <ImageUpload
            onImageSelect={setSelectedFiles}
            maxFiles={maxFiles}
            acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
            maxFileSize={10 * 1024 * 1024} // 10MB
          />
        </CardContent>
      </Card>

      {/* Processing Options */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium">Processing Options</h3>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Operation Type */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-3">
              Operation Type
            </label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  operationType === 'declutter'
                    ? 'border-royal-500 bg-royal-500/10'
                    : 'border-space-700 hover:border-space-600'
                }`}
                onClick={() => setOperationType('declutter')}
              >
                <div className="flex items-center space-x-3">
                  <Sparkles className="h-5 w-5 text-royal-400" />
                  <div>
                    <p className="font-medium text-foreground">Declutter</p>
                    <p className="text-xs text-space-400">1 photo per image</p>
                  </div>
                </div>
              </div>

              <div
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  operationType === 'staging'
                    ? 'border-royal-500 bg-royal-500/10'
                    : 'border-space-700 hover:border-space-600'
                }`}
                onClick={() => setOperationType('staging')}
              >
                <div className="flex items-center space-x-3">
                  <Wand2 className="h-5 w-5 text-royal-400" />
                  <div>
                    <p className="font-medium text-foreground">Virtual Staging</p>
                    <p className="text-xs text-space-400">1 photo per image</p>
                  </div>
                </div>
              </div>

              <div
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  operationType === 'chain'
                    ? 'border-royal-500 bg-royal-500/10'
                    : 'border-space-700 hover:border-space-600'
                }`}
                onClick={() => setOperationType('chain')}
              >
                <div className="flex items-center space-x-3">
                  <Camera className="h-5 w-5 text-royal-400" />
                  <div>
                    <p className="font-medium text-foreground">Declutter + Stage</p>
                    <p className="text-xs text-space-400">2 photos per image</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Room Type */}
          {(operationType === 'staging' || operationType === 'chain') && (
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Room Type
              </label>
              <select
                value={roomType}
                onChange={(e) => setRoomType(e.target.value)}
                className="w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20"
              >
                <option value="living_room">Living Room</option>
                <option value="bedroom">Bedroom</option>
                <option value="kitchen">Kitchen</option>
                <option value="dining_room">Dining Room</option>
                <option value="bathroom">Bathroom</option>
                <option value="office">Office</option>
                <option value="basement">Basement</option>
                <option value="outdoor">Outdoor</option>
              </select>
            </div>
          )}

          {/* Design Style */}
          {(operationType === 'staging' || operationType === 'chain') && (
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Design Style
              </label>
              <select
                value={designStyle}
                onChange={(e) => setDesignStyle(e.target.value)}
                className="w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20"
              >
                <option value="modern">Modern</option>
                <option value="contemporary">Contemporary</option>
                <option value="traditional">Traditional</option>
                <option value="minimalist">Minimalist</option>
                <option value="rustic">Rustic</option>
                <option value="industrial">Industrial</option>
                <option value="scandinavian">Scandinavian</option>
                <option value="luxury">Luxury</option>
              </select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-500/20 bg-red-500/10">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <p className="text-sm text-red-400">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Processing Queue */}
      {queue.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">Processing Queue</h3>
            <p className="text-sm text-space-400">
              {isProcessing ? `Processing image ${currentProcessingIndex + 1} of ${queue.length}` : 'Queue completed'}
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {queue.map((item, index) => (
                <div
                  key={item.id}
                  className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
                    index === currentProcessingIndex
                      ? 'border-royal-500 bg-royal-500/10'
                      : 'border-space-700'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <img
                      src={URL.createObjectURL(item.file)}
                      alt={`Queue item ${index + 1}`}
                      className="w-12 h-12 object-cover rounded"
                    />
                    <div>
                      <p className="text-sm font-medium text-foreground">
                        Image {index + 1}
                      </p>
                      <p className="text-xs text-space-400">
                        {item.file.name}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {item.status === 'pending' && (
                      <Clock className="h-4 w-4 text-space-400" />
                    )}
                    {item.status === 'processing' && (
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-royal-400 border-t-transparent"></div>
                    )}
                    {item.status === 'completed' && (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    )}
                    {item.status === 'failed' && (
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                    )}
                    <span className="text-xs text-foreground capitalize">
                      {item.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Process Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleProcessPhotos}
          variant="premium"
          size="lg"
          isLoading={isProcessing}
          disabled={!canProcessPhotos().canProcess}
          className="px-8"
        >
          {isProcessing ? 'Processing...' : `Process ${selectedFiles.length} Photo${selectedFiles.length !== 1 ? 's' : ''}`}
        </Button>
      </div>

      {/* Processing Results */}
      {jobs.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">Processing Results</h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {jobs.map((job) => (
                <div
                  key={job.id}
                  className="flex items-center justify-between p-4 rounded-lg border border-space-700"
                >
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <img
                        src={job.inputImageUrl}
                        alt="Input"
                        className="w-16 h-16 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => setViewingImage(job.inputImageUrl)}
                      />
                      <button
                        onClick={() => setViewingImage(job.inputImageUrl)}
                        className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity rounded-lg"
                      >
                        <Eye className="h-4 w-4 text-white" />
                      </button>
                    </div>
                    <div>
                      <p className="font-medium text-foreground capitalize">
                        {job.operationType}
                      </p>
                      <p className="text-sm text-space-400">
                        {job.photoCost} photo{job.photoCost > 1 ? 's' : ''} used
                      </p>
                      {job.error && (
                        <p className="text-xs text-red-400 mt-1">
                          {job.error}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    {job.status === 'completed' && job.outputImageUrl && (
                      <div className="relative">
                        <img
                          src={job.outputImageUrl}
                          alt="Output"
                          className="w-16 h-16 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => setViewingImage(job.outputImageUrl)}
                        />
                        <button
                          onClick={() => setViewingImage(job.outputImageUrl)}
                          className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity rounded-lg"
                        >
                          <Eye className="h-4 w-4 text-white" />
                        </button>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      {job.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5 text-green-400" />
                      ) : job.status === 'failed' ? (
                        <AlertTriangle className="h-5 w-5 text-red-400" />
                      ) : (
                        <Clock className="h-5 w-5 text-yellow-400" />
                      )}
                      <span className="text-sm text-foreground capitalize">
                        {job.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Image Viewer Modal */}
      {viewingImage && (
        <ImageViewer
          imageUrl={viewingImage}
          onClose={() => setViewingImage(null)}
        />
      )}
    </div>
  );
} 