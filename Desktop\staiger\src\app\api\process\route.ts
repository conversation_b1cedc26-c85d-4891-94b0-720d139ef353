import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { ContextEngine } from '@/services/context-engine/ContextEngine';
import { AIService } from '@/services/ai/AIService';

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const formData = await request.formData();
    
    const propertyId = formData.get('propertyId') as string;
    const operation = formData.get('operation') as string;
    const roomType = formData.get('roomType') as string;
    const designStyle = formData.get('designStyle') as string;
    const image = formData.get('image') as File;
    const forceReprocess = formData.get('forceReprocess') === 'true';

    if (!propertyId || !operation || !roomType || !image) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate operation type
    if (!['declutter', 'staging', 'chain'].includes(operation)) {
      return NextResponse.json(
        { error: 'Invalid operation type' },
        { status: 400 }
      );
    }

    // Basic file validation
    if (!image.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Please upload a valid image file' },
        { status: 400 }
      );
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (image.size > maxSize) {
      return NextResponse.json(
        { error: 'Image file size must be less than 10MB' },
        { status: 400 }
      );
    }

    // Get user session
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get property details
    const { data: property } = await supabase
      .from('properties')
      .select('*')
      .eq('id', propertyId)
      .single();

    if (!property) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    // Check if user owns the property
    if (property.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user can perform this operation (context/photo limits)
    const canProcess = await ContextEngine.canPerformOperation(
      operation as any,
      property as any,
      { id: session.user.id } as any
    );

    if (!canProcess.allowed) {
      return NextResponse.json(
        { error: canProcess.reason },
        { status: 400 }
      );
    }

    // Simple rate limiting check (server-side only)
    const rateLimitOk = await AIService.checkRateLimit(session.user.id);
    if (!rateLimitOk) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      );
    }

    // Process the image using AIService - this handles all AI operations securely
    const result = await AIService.processImage(
      session.user.id,
      propertyId,
      image,
      {
        operationType: operation as 'declutter' | 'staging' | 'chain',
        roomType,
        designStyle: designStyle || undefined,
        forceReprocess,
      },
      supabase
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Image processing failed' },
        { status: 500 }
      );
    }

    // Return the processed result
    return NextResponse.json({
      success: true,
      jobId: result.jobId,
      outputImageUrl: result.outputImageUrl,
      photosConsumed: result.photosConsumed,
    });

  } catch (error) {
    console.error('Error in process API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 