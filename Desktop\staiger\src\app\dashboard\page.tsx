"use client";

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Building2, Image as ImageIcon, Camera } from 'lucide-react';
import { getUserUsageStats, getUserProfile } from '@/lib/supabase';
import { useStore } from '@/store/useStore';

export default function DashboardPage() {
  const [user, setUser] = useState<any>(null);
  const [stats, setStats] = useState({
    properties: 0,
    processedImages: 0,
    photosUsed: 0,
  });
  const [loading, setLoading] = useState(true);
  const supabase = createClientComponentClient();
  const { setUser: setStoreUser } = useStore();

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        
        // Get authenticated user
        const { data: { user: authUser } } = await supabase.auth.getUser();
        if (!authUser) {
          window.location.href = '/login';
          return;
        }

        // Get user profile from database, create if not exists
        let userProfile = await getUserProfile(authUser.id);
        if (!userProfile) {
          // Create user profile if it doesn't exist
          const { error: createError } = await supabase
            .from('users')
            .insert({
              id: authUser.id,
              email: authUser.email || '',
              full_name: authUser.user_metadata?.full_name || '',
              plan: 'free',
              created_at: new Date().toISOString(),
              last_login: new Date().toISOString(),
            });

          if (!createError) {
            // Fetch the newly created profile
            userProfile = await getUserProfile(authUser.id);
          }
        }

        if (userProfile) {
          setUser(userProfile);
          setStoreUser({
            id: userProfile.id,
            email: userProfile.email,
            plan: userProfile.plan,
            name: userProfile.full_name,
          });
        }

        // Get user statistics
        const userStats = await getUserUsageStats(authUser.id);
        setStats({
          properties: userStats.totalProperties,
          processedImages: userStats.totalPhotosProcessed,
          photosUsed: userStats.thisMonthUsage,
        });

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [supabase, setStoreUser]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center space-y-4 animate-fade-up">
          <div className="h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent"></div>
          <p className="text-space-400">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 animate-fade-up">
      <div>
        <h1 className="text-3xl font-bold">
          Welcome back{user?.email ? `, ${user.email.split('@')[0]}` : ''}!
        </h1>
        <p className="text-space-400">Transform your property photos with AI-powered staging and decluttering</p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="animate-fade-up" style={{ animationDelay: '0.1s' }}>
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Building2 className="h-5 w-5 text-royal-400" />
                <h3 className="font-medium">Properties</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.properties}</div>
              <p className="text-sm text-space-400">Total properties added</p>
            </CardContent>
          </Card>
        </div>

        <div className="animate-fade-up" style={{ animationDelay: '0.2s' }}>
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5 text-royal-400" />
                <h3 className="font-medium">Photos Processed</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.processedImages}</div>
              <p className="text-sm text-space-400">Successfully transformed</p>
            </CardContent>
          </Card>
        </div>

        <div className="animate-fade-up" style={{ animationDelay: '0.3s' }}>
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Camera className="h-5 w-5 text-royal-400" />
                <h3 className="font-medium">Photos Used</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.photosUsed}</div>
              <p className="text-sm text-space-400">From your allowance</p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="animate-fade-up" style={{ animationDelay: '0.4s' }}>
          <Card>
            <CardHeader>
              <h3 className="font-medium">Get Started</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-space-400">
                  Welcome to Staiger AI! Start by adding your first property and uploading room photos to transform them with our AI technology.
                </p>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-royal-400" />
                    <span>Add a property with a valid address</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-royal-400" />
                    <span>Upload room photos for processing</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-royal-400" />
                    <span>Choose declutter, staging, or both</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-royal-400" />
                    <span>Download your transformed photos</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="animate-fade-up" style={{ animationDelay: '0.5s' }}>
          <Card>
            <CardHeader>
              <h3 className="font-medium">Your Plan</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-space-400">Current Plan</span>
                  <span className="font-medium text-royal-400 capitalize">
                    {user?.plan || 'Free'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-space-400">Photos per Property</span>
                  <span className="font-medium">
                    {(() => {
                      const plan = user?.plan || 'free';
                      switch (plan) {
                        case 'free': return '2';
                        case 'starter': return '5';
                        case 'pro': return '15';
                        case 'team': return '50';
                        default: return '2';
                      }
                    })()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-space-400">Properties</span>
                  <span className="font-medium">Unlimited</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-space-400">Features</span>
                  <span className="font-medium text-green-400">Staging + Declutter</span>
                </div>
                {(!user?.plan || user?.plan === 'free') && (
                  <div className="mt-4 p-3 bg-royal-500/10 border border-royal-500/20 rounded-lg">
                    <p className="text-xs text-royal-300">
                      Upgrade to process more photos per property and unlock priority processing.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {stats.properties === 0 && (
        <div className="animate-fade-up" style={{ animationDelay: '0.6s' }}>
          <Card variant="premium">
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <div className="h-12 w-12 mx-auto rounded-full bg-royal-500/10 flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-royal-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-foreground">Ready to get started?</h3>
                  <p className="text-sm text-space-400 mt-1">
                    Add your first property to begin transforming photos with AI
                  </p>
                </div>
                <button 
                  onClick={() => window.location.href = '/dashboard/properties'}
                  className="px-6 py-2 bg-royal-500 text-white rounded-lg hover:bg-royal-600 transition-colors"
                >
                  Add Your First Property
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
} 