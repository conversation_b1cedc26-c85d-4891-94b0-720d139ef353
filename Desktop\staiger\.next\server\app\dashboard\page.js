/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/loading.tsx */ \"(rsc)/./src/app/dashboard/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFpZ2VyLz9lNzQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUHJvamVjdDJEcmVhbVxcXFxEZXNrdG9wXFxcXHN0YWlnZXJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/loading.tsx */ \"(ssr)/./src/app/dashboard/loading.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDbG9hZGluZy50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8/N2IyOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFByb2plY3QyRHJlYW1cXFxcRGVza3RvcFxcXFxzdGFpZ2VyXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxvYWRpbmcudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8/Y2ZjNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFByb2plY3QyRHJlYW1cXFxcRGVza3RvcFxcXFxzdGFpZ2VyXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_dashboard_DashboardNav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardNav */ \"(ssr)/./src/components/dashboard/DashboardNav.tsx\");\n/* harmony import */ var _components_ContextBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ContextBar */ \"(ssr)/./src/components/ContextBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getUser = async ()=>{\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                window.location.href = \"/login\";\n                return;\n            }\n            setUser(user);\n            setLoading(false);\n        };\n        getUser();\n    }, [\n        supabase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 w-8 animate-spin rounded-full border-2 border-royal-500 border-t-transparent\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-space-900 text-foreground\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardNav__WEBPACK_IMPORTED_MODULE_3__.DashboardNav, {\n                sidebarOpen: sidebarOpen,\n                setSidebarOpen: setSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContextBar__WEBPACK_IMPORTED_MODULE_4__.ContextBar, {\n                        sidebarOpen: sidebarOpen,\n                        setSidebarOpen: setSidebarOpen\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto p-4 md:p-6 animate-fade-up\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/loading.tsx":
/*!***************************************!*\
  !*** ./src/app/dashboard/loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-4 animate-fade-up\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-space-400\",\n                    children: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRWUsU0FBU0E7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDQztvQkFBRUQsV0FBVTs4QkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8uL3NyYy9hcHAvZGFzaGJvYXJkL2xvYWRpbmcudHN4PzQ3ODYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbWluLWgtc2NyZWVuIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNCBhbmltYXRlLWZhZGUtdXBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIGFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgYm9yZGVyLTQgYm9yZGVyLXJveWFsLTUwMCBib3JkZXItdC10cmFuc3BhcmVudFwiPjwvZGl2PlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc3BhY2UtNDAwXCI+TG9hZGluZyB5b3VyIGRhc2hib2FyZC4uLjwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59ICJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_Camera_Image_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Camera,Image!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Camera_Image_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Camera,Image!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Camera_Image_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Camera,Image!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction DashboardPage() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        properties: 0,\n        processedImages: 0,\n        photosUsed: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const { setUser: setStoreUser } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadDashboardData = async ()=>{\n            try {\n                setLoading(true);\n                // Get authenticated user\n                const { data: { user: authUser } } = await supabase.auth.getUser();\n                if (!authUser) {\n                    window.location.href = \"/login\";\n                    return;\n                }\n                // Get user profile from database, create if not exists\n                let userProfile = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.getUserProfile)(authUser.id);\n                if (!userProfile) {\n                    // Create user profile if it doesn't exist\n                    const { error: createError } = await supabase.from(\"users\").insert({\n                        id: authUser.id,\n                        email: authUser.email || \"\",\n                        full_name: authUser.user_metadata?.full_name || \"\",\n                        plan: \"free\",\n                        created_at: new Date().toISOString(),\n                        last_login: new Date().toISOString()\n                    });\n                    if (!createError) {\n                        // Fetch the newly created profile\n                        userProfile = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.getUserProfile)(authUser.id);\n                    }\n                }\n                if (userProfile) {\n                    setUser(userProfile);\n                    setStoreUser({\n                        id: userProfile.id,\n                        email: userProfile.email,\n                        plan: userProfile.plan,\n                        name: userProfile.full_name\n                    });\n                }\n                // Get user statistics\n                const userStats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.getUserUsageStats)(authUser.id);\n                setStats({\n                    properties: userStats.totalProperties,\n                    processedImages: userStats.totalPhotosProcessed,\n                    photosUsed: userStats.thisMonthUsage\n                });\n            } catch (error) {\n                console.error(\"Error loading dashboard data:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadDashboardData();\n    }, [\n        supabase,\n        setStoreUser\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4 animate-fade-up\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-space-400\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 animate-fade-up\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: [\n                            \"Welcome back\",\n                            user?.email ? `, ${user.email.split(\"@\")[0]}` : \"\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-space-400\",\n                        children: \"Transform your property photos with AI-powered staging and decluttering\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-fade-up\",\n                        style: {\n                            animationDelay: \"0.1s\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Camera_Image_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 text-royal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Properties\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.properties\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-space-400\",\n                                            children: \"Total properties added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-fade-up\",\n                        style: {\n                            animationDelay: \"0.2s\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Camera_Image_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 text-royal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Photos Processed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.processedImages\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-space-400\",\n                                            children: \"Successfully transformed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-fade-up\",\n                        style: {\n                            animationDelay: \"0.3s\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Camera_Image_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-royal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Photos Used\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.photosUsed\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-space-400\",\n                                            children: \"From your allowance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-fade-up\",\n                        style: {\n                            animationDelay: \"0.4s\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-space-400\",\n                                                children: \"Welcome to Staiger AI! Start by adding your first property and uploading room photos to transform them with our AI technology.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 w-2 rounded-full bg-royal-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Add a property with a valid address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 w-2 rounded-full bg-royal-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Upload room photos for processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 w-2 rounded-full bg-royal-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Choose declutter, staging, or both\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 w-2 rounded-full bg-royal-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Download your transformed photos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-fade-up\",\n                        style: {\n                            animationDelay: \"0.5s\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium\",\n                                        children: \"Your Plan\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-space-400\",\n                                                        children: \"Current Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-royal-400 capitalize\",\n                                                        children: user?.plan || \"Free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-space-400\",\n                                                        children: \"Photos per Property\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (()=>{\n                                                            const plan = user?.plan || \"free\";\n                                                            switch(plan){\n                                                                case \"free\":\n                                                                    return \"2\";\n                                                                case \"starter\":\n                                                                    return \"5\";\n                                                                case \"pro\":\n                                                                    return \"15\";\n                                                                case \"team\":\n                                                                    return \"50\";\n                                                                default:\n                                                                    return \"2\";\n                                                            }\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-space-400\",\n                                                        children: \"Properties\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Unlimited\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-space-400\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-green-400\",\n                                                        children: \"Staging + Declutter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            (!user?.plan || user?.plan === \"free\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 bg-royal-500/10 border border-royal-500/20 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-royal-300\",\n                                                    children: \"Upgrade to process more photos per property and unlock priority processing.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            stats.properties === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-fade-up\",\n                style: {\n                    animationDelay: \"0.6s\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    variant: \"premium\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 mx-auto rounded-full bg-royal-500/10 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Camera_Image_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-6 w-6 text-royal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-foreground\",\n                                            children: \"Ready to get started?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-space-400 mt-1\",\n                                            children: \"Add your first property to begin transforming photos with AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.href = \"/dashboard/properties\",\n                                    className: \"px-6 py-2 bg-royal-500 text-white rounded-lg hover:bg-royal-600 transition-colors\",\n                                    children: \"Add Your First Property\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ContextBar.tsx":
/*!***************************************!*\
  !*** ./src/components/ContextBar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextBar: () => (/* binding */ ContextBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContextBar auto */ \n\n\n\n\nfunction ContextBar({ sidebarOpen, setSidebarOpen }) {\n    const photosUsed = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.photosUsed);\n    const photoLimit = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.photoLimit);\n    const plan = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.plan);\n    const user = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.user);\n    // Ensure we show correct values even if user isn't in database yet\n    const currentPlan = user?.plan || plan || \"free\";\n    const currentLimit = photoLimit || 2; // Default to free plan limit\n    const currentUsed = photosUsed || 0;\n    const percentage = currentUsed / currentLimit * 100;\n    const isNearLimit = percentage > 80;\n    const remaining = currentLimit - currentUsed;\n    const planIcons = {\n        free: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        starter: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        pro: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        team: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    const PlanIcon = planIcons[currentPlan] || _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-b border-space-800 bg-space-900/50 px-4 md:px-6 py-3 animate-slide-in\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            className: \"lg:hidden h-8 w-8 p-0 text-space-400 hover:text-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanIcon, {\n                                    className: \"h-4 w-4 text-royal-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-foreground capitalize\",\n                                    children: [\n                                        currentPlan,\n                                        \" Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 md:space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs md:text-sm text-space-400 hidden sm:inline\",\n                                    children: \"Photos remaining:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs md:text-sm text-space-400 sm:hidden\",\n                                    children: \"Photos:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs md:text-sm font-medium\", isNearLimit ? \"text-yellow-400\" : \"text-foreground\"),\n                                    children: [\n                                        remaining,\n                                        \"/\",\n                                        currentLimit\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:block w-20 md:w-32 h-2 rounded-full p-0.5 animate-gradient-flow\",\n                            style: {\n                                background: isNearLimit ? \"linear-gradient(45deg, #eab308, #ef4444, #f97316, #eab308, #ef4444)\" : \"linear-gradient(45deg, #0066ff, #9333ea, #7c3aed, #0066ff, #9333ea)\",\n                                backgroundSize: \"300% 300%\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-full bg-space-700 rounded-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-full transition-all duration-700 ease-out rounded-full transform origin-left\", isNearLimit ? \"bg-gradient-to-r from-yellow-500 to-red-500\" : \"bg-gradient-to-r from-royal-500 to-purple-600\", percentage > 0 && \"animate-scale-in-x\"),\n                                    style: {\n                                        width: `${Math.min(percentage, 100)}%`,\n                                        animationDelay: \"0.3s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        isNearLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse-glow rounded-lg bg-yellow-500/10 px-2 py-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-yellow-400\",\n                                children: \"Low\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ContextBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardNav.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/DashboardNav.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardNav: () => (/* binding */ DashboardNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardNav auto */ \n\n\n\n\n\n\nfunction DashboardNav({ sidebarOpen, setSidebarOpen }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__.createClientComponentClient)();\n    const { clearUser } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    const navItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Overview and analytics\"\n        },\n        {\n            name: \"Properties\",\n            href: \"/dashboard/properties\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Manage your properties\"\n        },\n        {\n            name: \"Process Photos\",\n            href: \"/dashboard/process\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"AI virtual staging\"\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/settings\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Account preferences\"\n        }\n    ];\n    const handleSignOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            clearUser();\n            window.location.href = \"/login\";\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const closeSidebar = ()=>setSidebarOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\",\n                onClick: closeSidebar\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-space-900/95 lg:bg-space-900/50 backdrop-blur-sm border-r border-space-700 flex flex-col\n        transform transition-transform duration-300 ease-in-out lg:transform-none\n        ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden absolute top-4 right-4 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: closeSidebar,\n                            className: \"h-8 w-8 p-0 text-space-400 hover:text-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-space-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard\",\n                            className: \"flex items-center space-x-3\",\n                            onClick: closeSidebar,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gradient-to-br from-royal-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-foreground\",\n                                    children: \"Staiger AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 px-4 py-6 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: navItems.map((item)=>{\n                                    const isActive = pathname === item.href || item.href !== \"/dashboard\" && pathname.startsWith(item.href);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        onClick: closeSidebar,\n                                        className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group ${isActive ? \"bg-royal-500/20 text-royal-400 border border-royal-500/30\" : \"text-space-400 hover:text-foreground hover:bg-space-800/50\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: `h-5 w-5 ${isActive ? \"text-royal-400\" : \"text-space-400 group-hover:text-foreground\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `font-medium ${isActive ? \"text-royal-400\" : \"text-space-300 group-hover:text-foreground\"}`,\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-space-500 group-hover:text-space-400\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-4 rounded-lg bg-gradient-to-br from-royal-500/10 to-purple-600/10 border border-royal-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 text-royal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-royal-400\",\n                                                children: \"Upgrade Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-space-400 mb-3\",\n                                        children: \"Get more photos per property and advanced features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"premium\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        onClick: ()=>{\n                                            window.location.href = \"/pricing\";\n                                            closeSidebar();\n                                        },\n                                        children: \"View Plans\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 rounded-lg bg-space-800/30 border border-space-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-space-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-space-300\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-space-400\",\n                                                        children: \"This Month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-foreground font-medium\",\n                                                        children: \"0 photos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-space-400\",\n                                                        children: \"Properties\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-foreground font-medium\",\n                                                        children: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-space-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleSignOut,\n                            className: \"w-full justify-start text-space-400 hover:text-foreground hover:bg-space-800/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                \"Sign Out\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-royal-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none hover:scale-105 active:scale-95\", {\n    variants: {\n        variant: {\n            default: \"bg-royal-500 text-white hover:bg-royal-600 shadow-lg hover:shadow-xl\",\n            destructive: \"bg-red-500 text-white hover:bg-red-600 shadow-lg hover:shadow-xl\",\n            outline: \"border border-royal-500 text-royal-500 hover:bg-royal-50 dark:hover:bg-royal-900 hover:shadow-lg\",\n            ghost: \"hover:bg-royal-50 text-royal-500 dark:hover:bg-royal-900\",\n            link: \"text-royal-500 underline-offset-4 hover:underline\",\n            premium: \"bg-gradient-to-r from-royal-500 to-purple-600 text-white hover:from-royal-600 hover:to-purple-700 shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-11 py-2 px-6\",\n            sm: \"h-9 px-4\",\n            lg: \"h-12 px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, size, isLoading, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props,\n        disabled: isLoading || props.disabled,\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Processing...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 49,\n            columnNumber: 11\n        }, undefined) : children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 42,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9CdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUV5RDtBQUN4QjtBQUM0QjtBQUU3RCxNQUFNRyxpQkFBaUJELDZEQUFHQSxDQUN4QixnU0FDQTtJQUNFRSxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLFNBQVM7UUFDWDtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNqQixpREFBVUEsQ0FDdkIsQ0FBQyxFQUFFa0IsU0FBUyxFQUFFYixPQUFPLEVBQUVPLElBQUksRUFBRU8sU0FBUyxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUM1RCxxQkFDRSw4REFBQ0M7UUFDQ0wsV0FBV2pCLDhDQUFFQSxDQUFDRSxlQUFlO1lBQUVFO1lBQVNPO1lBQU1NO1FBQVU7UUFDeERJLEtBQUtBO1FBQ0osR0FBR0QsS0FBSztRQUNURyxVQUFVTCxhQUFhRSxNQUFNRyxRQUFRO2tCQUVwQ0wsMEJBQ0MsOERBQUNNO1lBQUlQLFdBQVU7OzhCQUNiLDhEQUFDTztvQkFBSVAsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDUTs4QkFBSzs7Ozs7Ozs7Ozs7d0JBR1JOOzs7Ozs7QUFJUjtBQUdGSCxPQUFPVSxXQUFXLEdBQUc7QUFFYSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvLi9zcmMvY29tcG9uZW50cy91aS9CdXR0b24udHN4PzAxM2EiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgeyBCdXR0b25IVE1MQXR0cmlidXRlcywgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XHJcbmltcG9ydCB7IFZhcmlhbnRQcm9wcywgY3ZhIH0gZnJvbSAnY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5JztcclxuXHJcbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxyXG4gICdpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcm95YWwtNTAwIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgaG92ZXI6c2NhbGUtMTA1IGFjdGl2ZTpzY2FsZS05NScsXHJcbiAge1xyXG4gICAgdmFyaWFudHM6IHtcclxuICAgICAgdmFyaWFudDoge1xyXG4gICAgICAgIGRlZmF1bHQ6ICdiZy1yb3lhbC01MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1yb3lhbC02MDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCcsXHJcbiAgICAgICAgZGVzdHJ1Y3RpdmU6ICdiZy1yZWQtNTAwIHRleHQtd2hpdGUgaG92ZXI6YmctcmVkLTYwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsJyxcclxuICAgICAgICBvdXRsaW5lOiAnYm9yZGVyIGJvcmRlci1yb3lhbC01MDAgdGV4dC1yb3lhbC01MDAgaG92ZXI6Ymctcm95YWwtNTAgZGFyazpob3ZlcjpiZy1yb3lhbC05MDAgaG92ZXI6c2hhZG93LWxnJyxcclxuICAgICAgICBnaG9zdDogJ2hvdmVyOmJnLXJveWFsLTUwIHRleHQtcm95YWwtNTAwIGRhcms6aG92ZXI6Ymctcm95YWwtOTAwJyxcclxuICAgICAgICBsaW5rOiAndGV4dC1yb3lhbC01MDAgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZScsXHJcbiAgICAgICAgcHJlbWl1bTogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1yb3lhbC01MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmZyb20tcm95YWwtNjAwIGhvdmVyOnRvLXB1cnBsZS03MDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCcsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNpemU6IHtcclxuICAgICAgICBkZWZhdWx0OiAnaC0xMSBweS0yIHB4LTYnLFxyXG4gICAgICAgIHNtOiAnaC05IHB4LTQnLFxyXG4gICAgICAgIGxnOiAnaC0xMiBweC04JyxcclxuICAgICAgICBpY29uOiAnaC0xMCB3LTEwJyxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcclxuICAgICAgdmFyaWFudDogJ2RlZmF1bHQnLFxyXG4gICAgICBzaXplOiAnZGVmYXVsdCcsXHJcbiAgICB9LFxyXG4gIH1cclxuKTtcclxuXHJcbmludGVyZmFjZSBCdXR0b25Qcm9wc1xyXG4gIGV4dGVuZHMgQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+LFxyXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xyXG4gIGlzTG9hZGluZz86IGJvb2xlYW47XHJcbn1cclxuXHJcbmNvbnN0IEJ1dHRvbiA9IGZvcndhcmRSZWY8SFRNTEJ1dHRvbkVsZW1lbnQsIEJ1dHRvblByb3BzPihcclxuICAoeyBjbGFzc05hbWUsIHZhcmlhbnQsIHNpemUsIGlzTG9hZGluZywgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4ge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGJ1dHRvblxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxyXG4gICAgICAgIHJlZj17cmVmfVxyXG4gICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nIHx8IHByb3BzLmRpc2FibGVkfVxyXG4gICAgICA+XHJcbiAgICAgICAge2lzTG9hZGluZyA/IChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudFwiIC8+XHJcbiAgICAgICAgICAgIDxzcGFuPlByb2Nlc3NpbmcuLi48L3NwYW4+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApIDogKFxyXG4gICAgICAgICAgY2hpbGRyZW5cclxuICAgICAgICApfVxyXG4gICAgICA8L2J1dHRvbj5cclxuICAgICk7XHJcbiAgfVxyXG4pO1xyXG5cclxuQnV0dG9uLmRpc3BsYXlOYW1lID0gJ0J1dHRvbic7XHJcblxyXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH07ICJdLCJuYW1lcyI6WyJmb3J3YXJkUmVmIiwiY24iLCJjdmEiLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJnaG9zdCIsImxpbmsiLCJwcmVtaXVtIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiY2xhc3NOYW1lIiwiaXNMb2FkaW5nIiwiY2hpbGRyZW4iLCJwcm9wcyIsInJlZiIsImJ1dHRvbiIsImRpc2FibGVkIiwiZGl2Iiwic3BhbiIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardContent,CardFooter auto */ \n\n\nconst Card = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = \"default\", isHoverable = true, children, ...props }, ref)=>{\n    const baseStyles = \"rounded-xl backdrop-blur-sm transition-all duration-300\";\n    const variants = {\n        default: \"bg-space-800/30 border border-space-700\",\n        premium: \"bg-gradient-to-br from-royal-500/10 to-purple-600/10 border border-royal-500/20\",\n        ghost: \"bg-space-800/10 border border-space-700/50\"\n    };\n    const hoverStyles = isHoverable ? \"hover:-translate-y-1 hover:shadow-2xl\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, variants[variant], hoverStyles, className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 23,\n        columnNumber: 7\n    }, undefined);\n});\nconst CardHeader = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined));\nconst CardContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined));\nconst CardFooter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nCardHeader.displayName = \"CardHeader\";\nCardContent.displayName = \"CardContent\";\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkImageFingerprint: () => (/* binding */ checkImageFingerprint),\n/* harmony export */   createProcessingJob: () => (/* binding */ createProcessingJob),\n/* harmony export */   createProperty: () => (/* binding */ createProperty),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getImageUrl: () => (/* binding */ getImageUrl),\n/* harmony export */   getUserProcessingJobs: () => (/* binding */ getUserProcessingJobs),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserProperties: () => (/* binding */ getUserProperties),\n/* harmony export */   getUserUsageStats: () => (/* binding */ getUserUsageStats),\n/* harmony export */   logUsage: () => (/* binding */ logUsage),\n/* harmony export */   storeImageFingerprint: () => (/* binding */ storeImageFingerprint),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   updateJobStatus: () => (/* binding */ updateJobStatus),\n/* harmony export */   updateProcessingJob: () => (/* binding */ updateProcessingJob),\n/* harmony export */   updatePropertyUsage: () => (/* binding */ updatePropertyUsage),\n/* harmony export */   updateUserProfile: () => (/* binding */ updateUserProfile),\n/* harmony export */   uploadImage: () => (/* binding */ uploadImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// User Profile Functions\nasync function getUserProfile(userId) {\n    const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n    if (error) {\n        console.error(\"Error fetching user profile:\", error);\n        return null;\n    }\n    return data;\n}\nasync function updateUserProfile(userId, updates) {\n    const { data, error } = await supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n    if (error) {\n        console.error(\"Error updating user profile:\", error);\n        throw error;\n    }\n    return data;\n}\n// Property Functions\nasync function getUserProperties(userId) {\n    const { data, error } = await supabase.from(\"properties\").select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n        ascending: false\n    });\n    if (error) {\n        console.error(\"Error fetching properties:\", error);\n        return [];\n    }\n    return data || [];\n}\nasync function createProperty(userId, address, propertyType, photoLimit, propertyIndex) {\n    const { data, error } = await supabase.from(\"properties\").insert({\n        user_id: userId,\n        address,\n        property_type: propertyType,\n        photo_limit: photoLimit,\n        property_index: propertyIndex,\n        photos_used: 0\n    }).select().single();\n    if (error) {\n        console.error(\"Error creating property:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function updatePropertyUsage(propertyId, photosUsed) {\n    const { data, error } = await supabase.from(\"properties\").update({\n        photos_used: photosUsed\n    }).eq(\"id\", propertyId).select().single();\n    if (error) {\n        console.error(\"Error updating property usage:\", error);\n        throw error;\n    }\n    return data;\n}\n// Processing Job Functions\nasync function createProcessingJob(userId, propertyId, operationType, photoCost, inputImageUrl, metadata = {}) {\n    const { data, error } = await supabase.from(\"processing_jobs\").insert({\n        user_id: userId,\n        property_id: propertyId,\n        operation_type: operationType,\n        photo_cost: photoCost,\n        input_image_url: inputImageUrl,\n        status: \"pending\",\n        metadata\n    }).select().single();\n    if (error) {\n        console.error(\"Error creating processing job:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function updateProcessingJob(jobId, updates) {\n    const { data, error } = await supabase.from(\"processing_jobs\").update(updates).eq(\"id\", jobId).select().single();\n    if (error) {\n        console.error(\"Error updating processing job:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function getUserProcessingJobs(userId, limit = 10) {\n    const { data, error } = await supabase.from(\"processing_jobs\").select(`\n      *,\n      properties (\n        address,\n        property_type\n      )\n    `).eq(\"user_id\", userId).order(\"created_at\", {\n        ascending: false\n    }).limit(limit);\n    if (error) {\n        console.error(\"Error fetching processing jobs:\", error);\n        return [];\n    }\n    return data || [];\n}\n// Usage Analytics Functions\nasync function getUserUsageStats(userId) {\n    try {\n        // Get total properties\n        const { count: totalProperties } = await supabase.from(\"properties\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"user_id\", userId);\n        // Get total photos processed\n        const { count: totalPhotosProcessed } = await supabase.from(\"processing_jobs\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"user_id\", userId).eq(\"status\", \"completed\");\n        // Get this month's usage\n        const startOfMonth = new Date();\n        startOfMonth.setDate(1);\n        startOfMonth.setHours(0, 0, 0, 0);\n        const { count: thisMonthUsage } = await supabase.from(\"processing_jobs\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"user_id\", userId).eq(\"status\", \"completed\").gte(\"created_at\", startOfMonth.toISOString());\n        // Get recent activity\n        const { data: recentActivity } = await supabase.from(\"processing_jobs\").select(`\n        *,\n        properties (\n          address\n        )\n      `).eq(\"user_id\", userId).order(\"created_at\", {\n            ascending: false\n        }).limit(5);\n        return {\n            totalProperties: totalProperties || 0,\n            totalPhotosProcessed: totalPhotosProcessed || 0,\n            thisMonthUsage: thisMonthUsage || 0,\n            recentActivity: recentActivity || []\n        };\n    } catch (error) {\n        console.error(\"Error fetching usage stats:\", error);\n        return {\n            totalProperties: 0,\n            totalPhotosProcessed: 0,\n            thisMonthUsage: 0,\n            recentActivity: []\n        };\n    }\n}\n// File Upload Functions\nasync function uploadImage(file, bucket, path) {\n    const { data, error } = await supabase.storage.from(bucket).upload(path, file, {\n        cacheControl: \"3600\",\n        upsert: false\n    });\n    if (error) {\n        throw error;\n    }\n    return data;\n}\nasync function getImageUrl(bucket, path) {\n    const { data } = supabase.storage.from(bucket).getPublicUrl(path);\n    return data.publicUrl;\n}\n// Image Fingerprint Functions\nasync function storeImageFingerprint(userId, propertyId, imageHash, imageUrl) {\n    const { data, error } = await supabase.from(\"image_fingerprints\").insert({\n        user_id: userId,\n        property_id: propertyId,\n        image_hash: imageHash,\n        image_url: imageUrl\n    }).select().single();\n    if (error) {\n        console.error(\"Error storing image fingerprint:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function checkImageFingerprint(userId, imageHash) {\n    const { data, error } = await supabase.from(\"image_fingerprints\").select(\"*\").eq(\"user_id\", userId).eq(\"image_hash\", imageHash).single();\n    if (error && error.code !== \"PGRST116\") {\n        console.error(\"Error checking image fingerprint:\", error);\n        throw error;\n    }\n    return data;\n}\n// Usage Logging Functions\nasync function logUsage(userId, propertyId, jobId, photosConsumed, operationType, metadata = {}) {\n    const { data, error } = await supabase.from(\"usage_logs\").insert({\n        user_id: userId,\n        property_id: propertyId,\n        job_id: jobId,\n        photos_consumed: photosConsumed,\n        operation_type: operationType,\n        metadata\n    }).select().single();\n    if (error) {\n        console.error(\"Error logging usage:\", error);\n        throw error;\n    }\n    return data;\n}\n// Job Status Update Function\nasync function updateJobStatus(jobId, status, outputImageUrl) {\n    try {\n        const updateData = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (outputImageUrl) {\n            updateData.output_image_url = outputImageUrl;\n        }\n        const { error } = await supabase.from(\"processing_jobs\").update(updateData).eq(\"id\", jobId);\n        if (error) {\n            console.error(\"Error updating job status:\", error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error updating job status:\", error);\n        return false;\n    }\n}\n// Dashboard Stats Functions\nasync function getDashboardStats(userId) {\n    try {\n        const [usageStats, userProfile] = await Promise.all([\n            getUserUsageStats(userId),\n            getUserProfile(userId)\n        ]);\n        return {\n            ...usageStats,\n            userPlan: userProfile?.plan || \"free\"\n        };\n    } catch (error) {\n        console.error(\"Error fetching dashboard stats:\", error);\n        return {\n            totalProperties: 0,\n            totalPhotosProcessed: 0,\n            thisMonthUsage: 0,\n            recentActivity: [],\n            userPlan: \"free\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/index.ts":
/*!********************************!*\
  !*** ./src/lib/utils/index.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   generatePlaceholderPrompt: () => (/* binding */ generatePlaceholderPrompt),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getOperationLabel: () => (/* binding */ getOperationLabel),\n/* harmony export */   getPlanDetails: () => (/* binding */ getPlanDetails),\n/* harmony export */   isValidImageFile: () => (/* binding */ isValidImageFile)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatBytes(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\n}\nfunction getPlanDetails(tier) {\n    const plans = {\n        free: {\n            name: \"Free\",\n            price: 0,\n            features: [\n                \"2 context per property\",\n                \"Basic room types\",\n                \"Standard quality\"\n            ]\n        },\n        starter: {\n            name: \"Starter\",\n            price: 10,\n            features: [\n                \"5 context per property\",\n                \"All room types\",\n                \"Premium quality\",\n                \"Priority support\"\n            ]\n        },\n        pro: {\n            name: \"Pro\",\n            price: 30,\n            features: [\n                \"8 context per property\",\n                \"All room types\",\n                \"Ultra quality\",\n                \"Priority support\",\n                \"API access\"\n            ]\n        },\n        team: {\n            name: \"Team\",\n            price: 99,\n            features: [\n                \"10 context per property\",\n                \"All features\",\n                \"Ultra quality\",\n                \"Dedicated support\",\n                \"API access\",\n                \"Custom branding\"\n            ]\n        }\n    };\n    return plans[tier];\n}\nfunction getOperationLabel(operation) {\n    const labels = {\n        declutter: \"De-Clutter Room\",\n        stage: \"Stage Room\",\n        chain: \"De-Clutter & Stage\"\n    };\n    return labels[operation];\n}\nfunction delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction generatePlaceholderPrompt(roomType) {\n    const prompts = {\n        living: \"Modern living room with comfortable seating...\",\n        bedroom: \"Cozy bedroom with natural light...\",\n        kitchen: \"Contemporary kitchen with clean lines...\",\n        bathroom: \"Elegant bathroom with modern fixtures...\",\n        dining: \"Welcoming dining space with ambient lighting...\",\n        office: \"Professional home office setup...\"\n    };\n    return prompts[roomType] || \"Transform this space...\";\n}\nfunction isValidImageFile(file) {\n    const validTypes = [\n        \"image/jpeg\",\n        \"image/png\",\n        \"image/webp\"\n    ];\n    return validTypes.includes(file.type);\n}\nfunction getErrorMessage(error) {\n    if (error instanceof Error) return error.message;\n    return String(error);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/useStore.ts":
/*!*******************************!*\
  !*** ./src/store/useStore.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contextLimit: () => (/* binding */ contextLimit),\n/* harmony export */   contextUsed: () => (/* binding */ contextUsed),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// Helper function to get photo limits based on plan\nfunction getPlanPhotoLimit(plan) {\n    const limits = {\n        free: 2,\n        starter: 5,\n        pro: 15,\n        team: 50\n    };\n    return limits[plan] || 2;\n}\nconst useStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // User state\n        user: null,\n        setUser: (user)=>set({\n                user\n            }),\n        clearUser: ()=>set({\n                user: null,\n                selectedProperty: null,\n                photosUsed: 0,\n                photoLimit: 0\n            }),\n        // Property state\n        selectedProperty: null,\n        setSelectedProperty: (property)=>{\n            set({\n                selectedProperty: property\n            });\n            if (property) {\n                set({\n                    photosUsed: property.photosUsed,\n                    photoLimit: property.photoLimit\n                });\n            }\n        },\n        // Photo usage state\n        photosUsed: 0,\n        photoLimit: 0,\n        updatePhotoUsage: (used)=>{\n            set({\n                photosUsed: used\n            });\n            const { selectedProperty } = get();\n            if (selectedProperty) {\n                set({\n                    selectedProperty: {\n                        ...selectedProperty,\n                        photosUsed: used\n                    }\n                });\n            }\n        },\n        setPhotoLimits: (used, limit)=>set({\n                photosUsed: used,\n                photoLimit: limit\n            }),\n        // UI state\n        sidebarOpen: false,\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        // Processing state\n        isProcessing: false,\n        setIsProcessing: (processing)=>set({\n                isProcessing: processing\n            }),\n        // Error state\n        error: null,\n        setError: (error)=>set({\n                error\n            }),\n        clearError: ()=>set({\n                error: null\n            })\n    }), {\n    name: \"staiger-store\",\n    partialize: (state)=>({\n            user: state.user,\n            selectedProperty: state.selectedProperty,\n            photosUsed: state.photosUsed,\n            photoLimit: state.photoLimit\n        })\n}));\n// Aliases for backward compatibility with existing code that uses context terminology\nconst contextUsed = (state)=>state.photosUsed;\nconst contextLimit = (state)=>state.photoLimit;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/useStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"31c1960a3620\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGQ4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMxYzE5NjBhMzYyMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/loading.tsx":
/*!***************************************!*\
  !*** ./src/app/dashboard/loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\loading.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staiger AI - Virtual Staging Platform\",\n    description: \"Transform your real estate photos with AI-powered virtual staging and decluttering\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} bg-space-900 text-foreground antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVO2tCQUN4Qiw0RUFBQ0M7WUFBS0QsV0FBVyxDQUFDLEVBQUVSLCtKQUFlLENBQUMseUNBQXlDLENBQUM7c0JBQzNFSzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJTdGFpZ2VyIEFJIC0gVmlydHVhbCBTdGFnaW5nIFBsYXRmb3JtXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlRyYW5zZm9ybSB5b3VyIHJlYWwgZXN0YXRlIHBob3RvcyB3aXRoIEFJLXBvd2VyZWQgdmlydHVhbCBzdGFnaW5nIGFuZCBkZWNsdXR0ZXJpbmdcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gYmctc3BhY2UtOTAwIHRleHQtZm9yZWdyb3VuZCBhbnRpYWxpYXNlZGB9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFpZ2VyLy4vc3JjL2FwcC9mYXZpY29uLmljbz9jZDk0Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/zustand","vendor-chunks/use-sync-external-store"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();