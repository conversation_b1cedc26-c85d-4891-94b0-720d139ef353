-- Fix RLS Policies for Staiger AI
-- Run this in your Supabase SQL Editor

-- First, let's drop the existing restrictive storage policies
DROP POLICY IF EXISTS "Users can upload their own images" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own images" ON storage.objects;
DROP POLICY IF EXISTS "Public can view processed images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to view images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete images" ON storage.objects;

-- Create proper storage policies with user ownership
CREATE POLICY "Users can upload to their own folder" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'property-images' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

CREATE POLICY "Users can view their own images" ON storage.objects
FOR SELECT USING (
  bucket_id = 'property-images' AND
  (auth.uid()::text = (string_to_array(name, '/'))[1] OR bucket_id = 'property-images')
);

CREATE POLICY "Users can update their own images" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'property-images' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

CREATE POLICY "Users can delete their own images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'property-images' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Fix image_fingerprints policies with proper user checks
DROP POLICY IF EXISTS "Users can view own fingerprints" ON image_fingerprints;
DROP POLICY IF EXISTS "Users can create own fingerprints" ON image_fingerprints;
DROP POLICY IF EXISTS "Allow authenticated users to manage fingerprints" ON image_fingerprints;

CREATE POLICY "Users can view own fingerprints" ON image_fingerprints
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own fingerprints" ON image_fingerprints
FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Fix usage_logs policies with proper user checks
DROP POLICY IF EXISTS "Users can view own usage logs" ON usage_logs;
DROP POLICY IF EXISTS "Service can create usage logs" ON usage_logs;
DROP POLICY IF EXISTS "Allow authenticated users to manage usage logs" ON usage_logs;

CREATE POLICY "Users can view own usage logs" ON usage_logs
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own usage logs" ON usage_logs
FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Fix processing_jobs policies with proper user checks
DROP POLICY IF EXISTS "Users can view own jobs" ON processing_jobs;
DROP POLICY IF EXISTS "Users can create own jobs" ON processing_jobs;
DROP POLICY IF EXISTS "Users can update own jobs" ON processing_jobs;
DROP POLICY IF EXISTS "Allow authenticated users to manage jobs" ON processing_jobs;

CREATE POLICY "Users can view own jobs" ON processing_jobs
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own jobs" ON processing_jobs
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own jobs" ON processing_jobs
FOR UPDATE USING (auth.uid() = user_id);

-- Make sure the bucket exists and is public
INSERT INTO storage.buckets (id, name, public)
VALUES ('property-images', 'property-images', true)
ON CONFLICT (id) DO UPDATE SET public = true; 