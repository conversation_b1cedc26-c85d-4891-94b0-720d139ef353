/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/process/page";
exports.ids = ["app/dashboard/process/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprocess%2Fpage&page=%2Fdashboard%2Fprocess%2Fpage&appPaths=%2Fdashboard%2Fprocess%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprocess%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprocess%2Fpage&page=%2Fdashboard%2Fprocess%2Fpage&appPaths=%2Fdashboard%2Fprocess%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprocess%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'process',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/process/page.tsx */ \"(rsc)/./src/app/dashboard/process/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/loading.tsx */ \"(rsc)/./src/app/dashboard/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/process/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/process/page\",\n        pathname: \"/dashboard/process\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprocess%2Fpage&page=%2Fdashboard%2Fprocess%2Fpage&appPaths=%2Fdashboard%2Fprocess%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprocess%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFpZ2VyLz9lNzQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUHJvamVjdDJEcmVhbVxcXFxEZXNrdG9wXFxcXHN0YWlnZXJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/loading.tsx */ \"(ssr)/./src/app/dashboard/loading.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDbG9hZGluZy50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8/N2IyOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFByb2plY3QyRHJlYW1cXFxcRGVza3RvcFxcXFxzdGFpZ2VyXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxvYWRpbmcudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cprocess%5Cpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cprocess%5Cpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/process/page.tsx */ \"(ssr)/./src/app/dashboard/process/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDcHJvY2VzcyU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvP2ZjZDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcm9qZWN0MkRyZWFtXFxcXERlc2t0b3BcXFxcc3RhaWdlclxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwcm9jZXNzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cprocess%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_dashboard_DashboardNav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardNav */ \"(ssr)/./src/components/dashboard/DashboardNav.tsx\");\n/* harmony import */ var _components_ContextBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ContextBar */ \"(ssr)/./src/components/ContextBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getUser = async ()=>{\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                window.location.href = \"/login\";\n                return;\n            }\n            setUser(user);\n            setLoading(false);\n        };\n        getUser();\n    }, [\n        supabase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 w-8 animate-spin rounded-full border-2 border-royal-500 border-t-transparent\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-space-900 text-foreground\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardNav__WEBPACK_IMPORTED_MODULE_3__.DashboardNav, {\n                sidebarOpen: sidebarOpen,\n                setSidebarOpen: setSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContextBar__WEBPACK_IMPORTED_MODULE_4__.ContextBar, {\n                        sidebarOpen: sidebarOpen,\n                        setSidebarOpen: setSidebarOpen\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto p-4 md:p-6 animate-fade-up\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/loading.tsx":
/*!***************************************!*\
  !*** ./src/app/dashboard/loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-4 animate-fade-up\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-space-400\",\n                    children: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRWUsU0FBU0E7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDQztvQkFBRUQsV0FBVTs4QkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8uL3NyYy9hcHAvZGFzaGJvYXJkL2xvYWRpbmcudHN4PzQ3ODYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbWluLWgtc2NyZWVuIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNCBhbmltYXRlLWZhZGUtdXBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIGFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgYm9yZGVyLTQgYm9yZGVyLXJveWFsLTUwMCBib3JkZXItdC10cmFuc3BhcmVudFwiPjwvZGl2PlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc3BhY2UtNDAwXCI+TG9hZGluZyB5b3VyIGRhc2hib2FyZC4uLjwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59ICJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/process/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/process/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProcessPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ImageUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ImageUpload */ \"(ssr)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CheckCircle,Clock,Eye,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _hooks_useImageProcessing__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useImageProcessing */ \"(ssr)/./src/hooks/useImageProcessing.ts\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction ProcessPage() {\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [operationType, setOperationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"staging\");\n    const [roomType, setRoomType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"living_room\");\n    const [designStyle, setDesignStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"modern\");\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [viewingImage, setViewingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { selectedProperty, photosUsed, photoLimit, updatePhotoUsage } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_7__.useStore)();\n    const { processQueue, isProcessing, error, queue, currentProcessingIndex, clearQueue, clearError } = (0,_hooks_useImageProcessing__WEBPACK_IMPORTED_MODULE_6__.useImageProcessing)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const getOperationCost = (operation)=>{\n        const costs = {\n            declutter: 1,\n            staging: 1,\n            chain: 2\n        };\n        return costs[operation] || 1;\n    };\n    const canProcessPhotos = ()=>{\n        if (!selectedProperty) {\n            return {\n                canProcess: false,\n                reason: \"Please select a property first\"\n            };\n        }\n        if (selectedFiles.length === 0) {\n            return {\n                canProcess: false,\n                reason: \"Please upload at least one photo\"\n            };\n        }\n        const totalCost = selectedFiles.length * getOperationCost(operationType);\n        const remainingPhotos = photoLimit - photosUsed;\n        if (totalCost > remainingPhotos) {\n            return {\n                canProcess: false,\n                reason: `Not enough photos remaining. Need ${totalCost}, have ${remainingPhotos}`\n            };\n        }\n        return {\n            canProcess: true\n        };\n    };\n    const handleProcessPhotos = async ()=>{\n        const validation = canProcessPhotos();\n        if (!validation.canProcess) {\n            return;\n        }\n        try {\n            clearError();\n            clearQueue();\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user || !selectedProperty) return;\n            // Process all images using the queue system\n            const results = await processQueue({\n                propertyId: selectedProperty.id,\n                operation: operationType,\n                roomType,\n                designStyle,\n                images: selectedFiles\n            });\n            // Convert successful results to jobs\n            const newJobs = results.map((result, index)=>({\n                    id: result.jobId,\n                    status: \"completed\",\n                    operationType,\n                    inputImageUrl: URL.createObjectURL(selectedFiles[index]),\n                    outputImageUrl: result.outputImageUrl,\n                    photoCost: result.photosConsumed,\n                    createdAt: new Date().toISOString()\n                }));\n            // Add failed jobs from queue\n            const failedJobs = queue.filter((item)=>item.status === \"failed\").map((item, index)=>({\n                    id: item.id,\n                    status: \"failed\",\n                    operationType,\n                    inputImageUrl: URL.createObjectURL(item.file),\n                    photoCost: getOperationCost(operationType),\n                    createdAt: new Date().toISOString(),\n                    error: item.error\n                }));\n            // Update usage with successful results only\n            const totalPhotosUsed = results.reduce((sum, result)=>sum + result.photosConsumed, 0);\n            updatePhotoUsage(photosUsed + totalPhotosUsed);\n            // Add all jobs to the list\n            setJobs([\n                ...newJobs,\n                ...failedJobs,\n                ...jobs\n            ]);\n            setSelectedFiles([]);\n            // Log successful processing\n            console.log(`Successfully processed ${results.length}/${selectedFiles.length} images for user ${user.id}`);\n        } catch (error) {\n            console.error(\"Processing error:\", error);\n        }\n    };\n    // Image viewer modal\n    const ImageViewer = ({ imageUrl, onClose })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50\",\n            onClick: onClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-4xl max-h-[90vh] p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"absolute -top-2 -right-2 bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: imageUrl,\n                        alt: \"Full size view\",\n                        className: \"max-w-full max-h-full object-contain rounded-lg\",\n                        onClick: (e)=>e.stopPropagation()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n            lineNumber: 139,\n            columnNumber: 5\n        }, this);\n    if (!selectedProperty) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-yellow-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-foreground mb-2\",\n                            children: \"No Property Selected\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-space-400 mb-6\",\n                            children: \"Please select a property before processing photos\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>window.location.href = \"/dashboard/properties\",\n                            children: \"Select Property\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this);\n    }\n    const remainingPhotos = photoLimit - photosUsed;\n    const operationCost = getOperationCost(operationType);\n    const maxFiles = Math.floor(remainingPhotos / operationCost);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 animate-fade-up\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Process Photos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-space-400\",\n                        children: \"Transform your property photos with AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium\",\n                            children: \"Selected Property\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-foreground\",\n                                            children: selectedProperty.address\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-space-400 capitalize\",\n                                            children: selectedProperty.type\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-space-400\",\n                                            children: \"Photos Remaining\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-bold text-royal-400\",\n                                            children: [\n                                                remainingPhotos,\n                                                \"/\",\n                                                photoLimit\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium\",\n                                children: \"Upload Photos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-space-400\",\n                                children: [\n                                    \"Upload up to \",\n                                    maxFiles,\n                                    \" photos (\",\n                                    operationCost,\n                                    \" photo\",\n                                    operationCost > 1 ? \"s\" : \"\",\n                                    \" per \",\n                                    operationType,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUpload__WEBPACK_IMPORTED_MODULE_5__.ImageUpload, {\n                            onImageSelect: setSelectedFiles,\n                            maxFiles: maxFiles,\n                            acceptedFileTypes: [\n                                \"image/jpeg\",\n                                \"image/png\",\n                                \"image/webp\"\n                            ],\n                            maxFileSize: 10 * 1024 * 1024\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium\",\n                            children: \"Processing Options\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-foreground mb-3\",\n                                        children: \"Operation Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-4 rounded-lg border-2 cursor-pointer transition-all ${operationType === \"declutter\" ? \"border-royal-500 bg-royal-500/10\" : \"border-space-700 hover:border-space-600\"}`,\n                                                onClick: ()=>setOperationType(\"declutter\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-royal-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-foreground\",\n                                                                    children: \"Declutter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-space-400\",\n                                                                    children: \"1 photo per image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-4 rounded-lg border-2 cursor-pointer transition-all ${operationType === \"staging\" ? \"border-royal-500 bg-royal-500/10\" : \"border-space-700 hover:border-space-600\"}`,\n                                                onClick: ()=>setOperationType(\"staging\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-royal-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-foreground\",\n                                                                    children: \"Virtual Staging\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-space-400\",\n                                                                    children: \"1 photo per image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-4 rounded-lg border-2 cursor-pointer transition-all ${operationType === \"chain\" ? \"border-royal-500 bg-royal-500/10\" : \"border-space-700 hover:border-space-600\"}`,\n                                                onClick: ()=>setOperationType(\"chain\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5 text-royal-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-foreground\",\n                                                                    children: \"Declutter + Stage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-space-400\",\n                                                                    children: \"2 photos per image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            (operationType === \"staging\" || operationType === \"chain\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-foreground mb-2\",\n                                        children: \"Room Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: roomType,\n                                        onChange: (e)=>setRoomType(e.target.value),\n                                        className: \"w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"living_room\",\n                                                children: \"Living Room\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"bedroom\",\n                                                children: \"Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"kitchen\",\n                                                children: \"Kitchen\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"dining_room\",\n                                                children: \"Dining Room\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"bathroom\",\n                                                children: \"Bathroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"office\",\n                                                children: \"Office\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"basement\",\n                                                children: \"Basement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"outdoor\",\n                                                children: \"Outdoor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            (operationType === \"staging\" || operationType === \"chain\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-foreground mb-2\",\n                                        children: \"Design Style\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: designStyle,\n                                        onChange: (e)=>setDesignStyle(e.target.value),\n                                        className: \"w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"modern\",\n                                                children: \"Modern\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"contemporary\",\n                                                children: \"Contemporary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"traditional\",\n                                                children: \"Traditional\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"minimalist\",\n                                                children: \"Minimalist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"rustic\",\n                                                children: \"Rustic\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"industrial\",\n                                                children: \"Industrial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"scandinavian\",\n                                                children: \"Scandinavian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"luxury\",\n                                                children: \"Luxury\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-red-500/20 bg-red-500/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-red-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-400\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, this),\n            queue.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium\",\n                                children: \"Processing Queue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-space-400\",\n                                children: isProcessing ? `Processing image ${currentProcessingIndex + 1} of ${queue.length}` : \"Queue completed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: queue.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center justify-between p-3 rounded-lg border transition-all ${index === currentProcessingIndex ? \"border-royal-500 bg-royal-500/10\" : \"border-space-700\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: URL.createObjectURL(item.file),\n                                                    alt: `Queue item ${index + 1}`,\n                                                    className: \"w-12 h-12 object-cover rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-foreground\",\n                                                            children: [\n                                                                \"Image \",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-space-400\",\n                                                            children: item.file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-space-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-2 border-royal-400 border-t-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.status === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-foreground capitalize\",\n                                                    children: item.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: handleProcessPhotos,\n                    variant: \"premium\",\n                    size: \"lg\",\n                    isLoading: isProcessing,\n                    disabled: !canProcessPhotos().canProcess,\n                    className: \"px-8\",\n                    children: isProcessing ? \"Processing...\" : `Process ${selectedFiles.length} Photo${selectedFiles.length !== 1 ? \"s\" : \"\"}`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            jobs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium\",\n                            children: \"Processing Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: jobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 rounded-lg border border-space-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: job.inputImageUrl,\n                                                            alt: \"Input\",\n                                                            className: \"w-16 h-16 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity\",\n                                                            onClick: ()=>setViewingImage(job.inputImageUrl)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewingImage(job.inputImageUrl),\n                                                            className: \"absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-foreground capitalize\",\n                                                            children: job.operationType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-space-400\",\n                                                            children: [\n                                                                job.photoCost,\n                                                                \" photo\",\n                                                                job.photoCost > 1 ? \"s\" : \"\",\n                                                                \" used\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        job.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-400 mt-1\",\n                                                            children: job.error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                job.status === \"completed\" && job.outputImageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: job.outputImageUrl,\n                                                            alt: \"Output\",\n                                                            className: \"w-16 h-16 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity\",\n                                                            onClick: ()=>setViewingImage(job.outputImageUrl)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewingImage(job.outputImageUrl),\n                                                            className: \"absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        job.status === \"completed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 25\n                                                        }, this) : job.status === \"failed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CheckCircle_Clock_Eye_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 text-yellow-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-foreground capitalize\",\n                                                            children: job.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, job.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 427,\n                columnNumber: 9\n            }, this),\n            viewingImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageViewer, {\n                imageUrl: viewingImage,\n                onClose: ()=>setViewingImage(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\process\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/process/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ContextBar.tsx":
/*!***************************************!*\
  !*** ./src/components/ContextBar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextBar: () => (/* binding */ ContextBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContextBar auto */ \n\n\n\n\nfunction ContextBar({ sidebarOpen, setSidebarOpen }) {\n    const photosUsed = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.photosUsed);\n    const photoLimit = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.photoLimit);\n    const plan = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.plan);\n    const user = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.user);\n    // Ensure we show correct values even if user isn't in database yet\n    const currentPlan = user?.plan || plan || \"free\";\n    const currentLimit = photoLimit || 2; // Default to free plan limit\n    const currentUsed = photosUsed || 0;\n    const percentage = currentUsed / currentLimit * 100;\n    const isNearLimit = percentage > 80;\n    const remaining = currentLimit - currentUsed;\n    const planIcons = {\n        free: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        starter: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        pro: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        team: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    const PlanIcon = planIcons[currentPlan] || _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-b border-space-800 bg-space-900/50 px-4 md:px-6 py-3 animate-slide-in\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            className: \"lg:hidden h-8 w-8 p-0 text-space-400 hover:text-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanIcon, {\n                                    className: \"h-4 w-4 text-royal-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-foreground capitalize\",\n                                    children: [\n                                        currentPlan,\n                                        \" Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 md:space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs md:text-sm text-space-400 hidden sm:inline\",\n                                    children: \"Photos remaining:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs md:text-sm text-space-400 sm:hidden\",\n                                    children: \"Photos:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs md:text-sm font-medium\", isNearLimit ? \"text-yellow-400\" : \"text-foreground\"),\n                                    children: [\n                                        remaining,\n                                        \"/\",\n                                        currentLimit\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:block w-20 md:w-32 h-2 rounded-full p-0.5 animate-gradient-flow\",\n                            style: {\n                                background: isNearLimit ? \"linear-gradient(45deg, #eab308, #ef4444, #f97316, #eab308, #ef4444)\" : \"linear-gradient(45deg, #0066ff, #9333ea, #7c3aed, #0066ff, #9333ea)\",\n                                backgroundSize: \"300% 300%\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-full bg-space-700 rounded-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-full transition-all duration-700 ease-out rounded-full transform origin-left\", isNearLimit ? \"bg-gradient-to-r from-yellow-500 to-red-500\" : \"bg-gradient-to-r from-royal-500 to-purple-600\", percentage > 0 && \"animate-scale-in-x\"),\n                                    style: {\n                                        width: `${Math.min(percentage, 100)}%`,\n                                        animationDelay: \"0.3s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        isNearLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse-glow rounded-lg bg-yellow-500/10 px-2 py-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-yellow-400\",\n                                children: \"Low\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ContextBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageUpload: () => (/* binding */ ImageUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(ssr)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ ImageUpload auto */ \n\n\n\n\n\nfunction ImageUpload({ onImageSelect, maxFiles = 5, acceptedFileTypes = [\n    \"image/jpeg\",\n    \"image/png\",\n    \"image/webp\"\n], maxFileSize = 10 * 1024 * 1024 }) {\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDragActive, setIsDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        const newFiles = [\n            ...uploadedFiles,\n            ...acceptedFiles\n        ].slice(0, maxFiles);\n        setUploadedFiles(newFiles);\n        onImageSelect(newFiles);\n        setIsDragActive(false);\n    }, [\n        uploadedFiles,\n        maxFiles,\n        onImageSelect\n    ]);\n    const { getRootProps, getInputProps } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes.reduce((acc, type)=>{\n            acc[type] = [];\n            return acc;\n        }, {}),\n        maxSize: maxFileSize,\n        maxFiles,\n        onDragEnter: ()=>setIsDragActive(true),\n        onDragLeave: ()=>setIsDragActive(false)\n    });\n    const removeFile = (index)=>{\n        const newFiles = uploadedFiles.filter((_, i)=>i !== index);\n        setUploadedFiles(newFiles);\n        onImageSelect(newFiles);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...getRootProps(),\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative rounded-lg border-2 border-dashed p-8 text-center transition-all duration-300 cursor-pointer\", isDragActive ? \"border-royal-400 bg-royal-500/10 scale-105\" : \"border-space-600 hover:border-royal-400 hover:bg-space-800/50\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 animate-fade-up\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto h-12 w-12 rounded-full bg-royal-500/10 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"h-6 w-6 transition-colors duration-200\", isDragActive ? \"text-royal-400\" : \"text-space-400\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-foreground\",\n                                        children: isDragActive ? \"Drop your images here\" : \"Upload room photos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-space-400\",\n                                        children: \"Drag & drop or click to select files\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-space-500 mt-2\",\n                                        children: [\n                                            \"Supports JPEG, PNG, WebP • Max \",\n                                            maxFiles,\n                                            \" files • Up to \",\n                                            maxFileSize / (1024 * 1024),\n                                            \"MB each\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-foreground\",\n                        children: [\n                            \"Uploaded Files (\",\n                            uploadedFiles.length,\n                            \"/\",\n                            maxFiles,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-3 sm:grid-cols-2 lg:grid-cols-3\",\n                        children: uploadedFiles.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group rounded-lg border border-space-700 bg-space-800/30 p-3 animate-scale-in\",\n                                style: {\n                                    animationDelay: `${index * 0.1}s`\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-8 w-8 text-royal-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-foreground truncate\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-space-400\",\n                                                        children: [\n                                                            (file.size / (1024 * 1024)).toFixed(2),\n                                                            \" MB\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-500 text-white opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600\",\n                                        onClick: ()=>removeFile(index),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, `${file.name}-${index}`, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ImageUpload.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ImageUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardNav.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/DashboardNav.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardNav: () => (/* binding */ DashboardNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardNav auto */ \n\n\n\n\n\n\nfunction DashboardNav({ sidebarOpen, setSidebarOpen }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__.createClientComponentClient)();\n    const { clearUser } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    const navItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Overview and analytics\"\n        },\n        {\n            name: \"Properties\",\n            href: \"/dashboard/properties\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Manage your properties\"\n        },\n        {\n            name: \"Process Photos\",\n            href: \"/dashboard/process\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"AI virtual staging\"\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/settings\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Account preferences\"\n        }\n    ];\n    const handleSignOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            clearUser();\n            window.location.href = \"/login\";\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const closeSidebar = ()=>setSidebarOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\",\n                onClick: closeSidebar\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-space-900/95 lg:bg-space-900/50 backdrop-blur-sm border-r border-space-700 flex flex-col\n        transform transition-transform duration-300 ease-in-out lg:transform-none\n        ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden absolute top-4 right-4 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: closeSidebar,\n                            className: \"h-8 w-8 p-0 text-space-400 hover:text-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-space-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard\",\n                            className: \"flex items-center space-x-3\",\n                            onClick: closeSidebar,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gradient-to-br from-royal-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-foreground\",\n                                    children: \"Staiger AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 px-4 py-6 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: navItems.map((item)=>{\n                                    const isActive = pathname === item.href || item.href !== \"/dashboard\" && pathname.startsWith(item.href);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        onClick: closeSidebar,\n                                        className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group ${isActive ? \"bg-royal-500/20 text-royal-400 border border-royal-500/30\" : \"text-space-400 hover:text-foreground hover:bg-space-800/50\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: `h-5 w-5 ${isActive ? \"text-royal-400\" : \"text-space-400 group-hover:text-foreground\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `font-medium ${isActive ? \"text-royal-400\" : \"text-space-300 group-hover:text-foreground\"}`,\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-space-500 group-hover:text-space-400\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-4 rounded-lg bg-gradient-to-br from-royal-500/10 to-purple-600/10 border border-royal-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 text-royal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-royal-400\",\n                                                children: \"Upgrade Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-space-400 mb-3\",\n                                        children: \"Get more photos per property and advanced features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"premium\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        onClick: ()=>{\n                                            window.location.href = \"/pricing\";\n                                            closeSidebar();\n                                        },\n                                        children: \"View Plans\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 rounded-lg bg-space-800/30 border border-space-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-space-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-space-300\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-space-400\",\n                                                        children: \"This Month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-foreground font-medium\",\n                                                        children: \"0 photos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-space-400\",\n                                                        children: \"Properties\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-foreground font-medium\",\n                                                        children: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-space-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleSignOut,\n                            className: \"w-full justify-start text-space-400 hover:text-foreground hover:bg-space-800/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                \"Sign Out\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkTmF2LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU4QztBQUNqQjtBQVVQO0FBQzBCO0FBQzRCO0FBQ2hDO0FBY3JDLFNBQVNhLGFBQWEsRUFBRUMsV0FBVyxFQUFFQyxjQUFjLEVBQXFCO0lBQzdFLE1BQU1DLFdBQVdoQiw0REFBV0E7SUFDNUIsTUFBTWlCLFdBQVdOLDBGQUEyQkE7SUFDNUMsTUFBTSxFQUFFTyxTQUFTLEVBQUUsR0FBR04seURBQVFBO0lBRTlCLE1BQU1PLFdBQXNCO1FBQzFCO1lBQ0VDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNcEIsOElBQWVBO1lBQ3JCcUIsYUFBYTtRQUNmO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU1uQiw4SUFBU0E7WUFDZm9CLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNbEIsOElBQU1BO1lBQ1ptQixhQUFhO1FBQ2Y7UUFDQTtZQUNFSCxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTWpCLDhJQUFRQTtZQUNka0IsYUFBYTtRQUNmO0tBQ0Q7SUFFRCxNQUFNQyxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLE1BQU1QLFNBQVNRLElBQUksQ0FBQ0MsT0FBTztZQUMzQlI7WUFDQVMsT0FBT0MsUUFBUSxDQUFDUCxJQUFJLEdBQUc7UUFDekIsRUFBRSxPQUFPUSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxzQkFBc0JBO1FBQ3RDO0lBQ0Y7SUFFQSxNQUFNRSxlQUFlLElBQU1oQixlQUFlO0lBRTFDLHFCQUNFOztZQUVHRCw2QkFDQyw4REFBQ2tCO2dCQUNDQyxXQUFVO2dCQUNWQyxTQUFTSDs7Ozs7OzBCQUtiLDhEQUFDSTtnQkFBSUYsV0FBVyxDQUFDOzs7UUFHZixFQUFFbkIsY0FBYyxrQkFBa0IscUNBQXFDO01BQ3pFLENBQUM7O2tDQUVDLDhEQUFDa0I7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN2Qix5REFBTUE7NEJBQ0wwQixTQUFROzRCQUNSQyxNQUFLOzRCQUNMSCxTQUFTSDs0QkFDVEUsV0FBVTtzQ0FFViw0RUFBQ3hCLCtJQUFDQTtnQ0FBQ3dCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS2pCLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ2hDLGtEQUFJQTs0QkFBQ29CLE1BQUs7NEJBQWFZLFdBQVU7NEJBQThCQyxTQUFTSDs7OENBQ3ZFLDhEQUFDQztvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzNCLCtJQUFLQTt3Q0FBQzJCLFdBQVU7Ozs7Ozs7Ozs7OzhDQUVuQiw4REFBQ0s7b0NBQUtMLFdBQVU7OENBQW9DOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLeEQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pkLFNBQVNvQixHQUFHLENBQUMsQ0FBQ0M7b0NBQ2IsTUFBTUMsV0FBV3pCLGFBQWF3QixLQUFLbkIsSUFBSSxJQUNwQ21CLEtBQUtuQixJQUFJLEtBQUssZ0JBQWdCTCxTQUFTMEIsVUFBVSxDQUFDRixLQUFLbkIsSUFBSTtvQ0FFOUQscUJBQ0UsOERBQUNwQixrREFBSUE7d0NBRUhvQixNQUFNbUIsS0FBS25CLElBQUk7d0NBQ2ZhLFNBQVNIO3dDQUNURSxXQUFXLENBQUMsbUZBQW1GLEVBQzdGUSxXQUNJLDhEQUNBLDZEQUNMLENBQUM7OzBEQUVGLDhEQUFDRCxLQUFLbEIsSUFBSTtnREFBQ1csV0FBVyxDQUFDLFFBQVEsRUFBRVEsV0FBVyxtQkFBbUIsNkNBQTZDLENBQUM7Ozs7OzswREFDN0csOERBQUNUO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVcsQ0FBQyxZQUFZLEVBQUVRLFdBQVcsbUJBQW1CLDZDQUE2QyxDQUFDO2tFQUN4R0QsS0FBS3BCLElBQUk7Ozs7OztvREFFWG9CLEtBQUtqQixXQUFXLGtCQUNmLDhEQUFDUzt3REFBSUMsV0FBVTtrRUFDWk8sS0FBS2pCLFdBQVc7Ozs7Ozs7Ozs7Ozs7dUNBaEJsQmlCLEtBQUtwQixJQUFJOzs7OztnQ0FzQnBCOzs7Ozs7MENBSUYsOERBQUNZO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDM0IsK0lBQUtBO2dEQUFDMkIsV0FBVTs7Ozs7OzBEQUNqQiw4REFBQ0s7Z0RBQUtMLFdBQVU7MERBQXFDOzs7Ozs7Ozs7Ozs7a0RBRXZELDhEQUFDVTt3Q0FBRVYsV0FBVTtrREFBOEI7Ozs7OztrREFHM0MsOERBQUN2Qix5REFBTUE7d0NBQ0wwQixTQUFRO3dDQUNSQyxNQUFLO3dDQUNMSixXQUFVO3dDQUNWQyxTQUFTOzRDQUNQUCxPQUFPQyxRQUFRLENBQUNQLElBQUksR0FBRzs0Q0FDdkJVO3dDQUNGO2tEQUNEOzs7Ozs7Ozs7Ozs7MENBTUgsOERBQUNDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDMUIsK0lBQVNBO2dEQUFDMEIsV0FBVTs7Ozs7OzBEQUNyQiw4REFBQ0s7Z0RBQUtMLFdBQVU7MERBQXFDOzs7Ozs7Ozs7Ozs7a0RBRXZELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0s7d0RBQUtMLFdBQVU7a0VBQWlCOzs7Ozs7a0VBQ2pDLDhEQUFDSzt3REFBS0wsV0FBVTtrRUFBOEI7Ozs7Ozs7Ozs7OzswREFFaEQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0s7d0RBQUtMLFdBQVU7a0VBQWlCOzs7Ozs7a0VBQ2pDLDhEQUFDSzt3REFBS0wsV0FBVTtrRUFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPdEQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDdkIseURBQU1BOzRCQUNMMEIsU0FBUTs0QkFDUkYsU0FBU1Y7NEJBQ1RTLFdBQVU7OzhDQUVWLDhEQUFDekIsK0lBQU1BO29DQUFDeUIsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPL0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFpZ2VyLy4vc3JjL2NvbXBvbmVudHMvZGFzaGJvYXJkL0Rhc2hib2FyZE5hdi50c3g/M2VhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IFxuICBMYXlvdXREYXNoYm9hcmQsIFxuICBCdWlsZGluZzIsIFxuICBDYW1lcmEsIFxuICBTZXR0aW5ncywgXG4gIENyb3duLFxuICBCYXJDaGFydDMsXG4gIExvZ091dCxcbiAgWFxufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0J1dHRvbic7XG5pbXBvcnQgeyBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQgfSBmcm9tICdAc3VwYWJhc2UvYXV0aC1oZWxwZXJzLW5leHRqcyc7XG5pbXBvcnQgeyB1c2VTdG9yZSB9IGZyb20gJ0Avc3RvcmUvdXNlU3RvcmUnO1xuXG5pbnRlcmZhY2UgTmF2SXRlbSB7XG4gIG5hbWU6IHN0cmluZztcbiAgaHJlZjogc3RyaW5nO1xuICBpY29uOiBSZWFjdC5Db21wb25lbnRUeXBlPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+O1xuICBkZXNjcmlwdGlvbj86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIERhc2hib2FyZE5hdlByb3BzIHtcbiAgc2lkZWJhck9wZW46IGJvb2xlYW47XG4gIHNldFNpZGViYXJPcGVuOiAob3BlbjogYm9vbGVhbikgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIERhc2hib2FyZE5hdih7IHNpZGViYXJPcGVuLCBzZXRTaWRlYmFyT3BlbiB9OiBEYXNoYm9hcmROYXZQcm9wcykge1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50KCk7XG4gIGNvbnN0IHsgY2xlYXJVc2VyIH0gPSB1c2VTdG9yZSgpO1xuXG4gIGNvbnN0IG5hdkl0ZW1zOiBOYXZJdGVtW10gPSBbXG4gICAge1xuICAgICAgbmFtZTogJ0Rhc2hib2FyZCcsXG4gICAgICBocmVmOiAnL2Rhc2hib2FyZCcsXG4gICAgICBpY29uOiBMYXlvdXREYXNoYm9hcmQsXG4gICAgICBkZXNjcmlwdGlvbjogJ092ZXJ2aWV3IGFuZCBhbmFseXRpY3MnXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnUHJvcGVydGllcycsXG4gICAgICBocmVmOiAnL2Rhc2hib2FyZC9wcm9wZXJ0aWVzJyxcbiAgICAgIGljb246IEJ1aWxkaW5nMixcbiAgICAgIGRlc2NyaXB0aW9uOiAnTWFuYWdlIHlvdXIgcHJvcGVydGllcydcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICdQcm9jZXNzIFBob3RvcycsXG4gICAgICBocmVmOiAnL2Rhc2hib2FyZC9wcm9jZXNzJyxcbiAgICAgIGljb246IENhbWVyYSxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQUkgdmlydHVhbCBzdGFnaW5nJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ1NldHRpbmdzJyxcbiAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL3NldHRpbmdzJyxcbiAgICAgIGljb246IFNldHRpbmdzLFxuICAgICAgZGVzY3JpcHRpb246ICdBY2NvdW50IHByZWZlcmVuY2VzJ1xuICAgIH0sXG4gIF07XG5cbiAgY29uc3QgaGFuZGxlU2lnbk91dCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduT3V0KCk7XG4gICAgICBjbGVhclVzZXIoKTtcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbic7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNpZ25pbmcgb3V0OicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgY2xvc2VTaWRlYmFyID0gKCkgPT4gc2V0U2lkZWJhck9wZW4oZmFsc2UpO1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBNb2JpbGUgb3ZlcmxheSAqL31cbiAgICAgIHtzaWRlYmFyT3BlbiAmJiAoXG4gICAgICAgIDxkaXYgXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay81MCB6LTQwIGxnOmhpZGRlblwiXG4gICAgICAgICAgb25DbGljaz17Y2xvc2VTaWRlYmFyfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIFNpZGViYXIgKi99XG4gICAgICA8bmF2IGNsYXNzTmFtZT17YFxuICAgICAgICBmaXhlZCBsZzpzdGF0aWMgaW5zZXQteS0wIGxlZnQtMCB6LTUwIHctNjQgYmctc3BhY2UtOTAwLzk1IGxnOmJnLXNwYWNlLTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlci1yIGJvcmRlci1zcGFjZS03MDAgZmxleCBmbGV4LWNvbFxuICAgICAgICB0cmFuc2Zvcm0gdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IGxnOnRyYW5zZm9ybS1ub25lXG4gICAgICAgICR7c2lkZWJhck9wZW4gPyAndHJhbnNsYXRlLXgtMCcgOiAnLXRyYW5zbGF0ZS14LWZ1bGwgbGc6dHJhbnNsYXRlLXgtMCd9XG4gICAgICBgfT5cbiAgICAgICAgey8qIE1vYmlsZSBjbG9zZSBidXR0b24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIGFic29sdXRlIHRvcC00IHJpZ2h0LTQgei0xMFwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgb25DbGljaz17Y2xvc2VTaWRlYmFyfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTAgdGV4dC1zcGFjZS00MDAgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIExvZ28vQnJhbmQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1zcGFjZS03MDBcIj5cbiAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZFwiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiIG9uQ2xpY2s9e2Nsb3NlU2lkZWJhcn0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yb3lhbC01MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxDcm93biBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmRcIj5TdGFpZ2VyIEFJPC9zcGFuPlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE5hdmlnYXRpb24gSXRlbXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTQgcHktNiBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAge25hdkl0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBpc0FjdGl2ZSA9IHBhdGhuYW1lID09PSBpdGVtLmhyZWYgfHwgXG4gICAgICAgICAgICAgICAgKGl0ZW0uaHJlZiAhPT0gJy9kYXNoYm9hcmQnICYmIHBhdGhuYW1lLnN0YXJ0c1dpdGgoaXRlbS5ocmVmKSk7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2Nsb3NlU2lkZWJhcn1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBweC00IHB5LTMgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZ3JvdXAgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNBY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1yb3lhbC01MDAvMjAgdGV4dC1yb3lhbC00MDAgYm9yZGVyIGJvcmRlci1yb3lhbC01MDAvMzAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1zcGFjZS00MDAgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kIGhvdmVyOmJnLXNwYWNlLTgwMC81MCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxpdGVtLmljb24gY2xhc3NOYW1lPXtgaC01IHctNSAke2lzQWN0aXZlID8gJ3RleHQtcm95YWwtNDAwJyA6ICd0ZXh0LXNwYWNlLTQwMCBncm91cC1ob3Zlcjp0ZXh0LWZvcmVncm91bmQnfWB9IC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7aXNBY3RpdmUgPyAndGV4dC1yb3lhbC00MDAnIDogJ3RleHQtc3BhY2UtMzAwIGdyb3VwLWhvdmVyOnRleHQtZm9yZWdyb3VuZCd9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zcGFjZS01MDAgZ3JvdXAtaG92ZXI6dGV4dC1zcGFjZS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogVXBncmFkZSBTZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBwLTQgcm91bmRlZC1sZyBiZy1ncmFkaWVudC10by1iciBmcm9tLXJveWFsLTUwMC8xMCB0by1wdXJwbGUtNjAwLzEwIGJvcmRlciBib3JkZXItcm95YWwtNTAwLzIwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XG4gICAgICAgICAgICAgIDxDcm93biBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcm95YWwtNDAwXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXJveWFsLTQwMFwiPlVwZ3JhZGUgUGxhbjwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNwYWNlLTQwMCBtYi0zXCI+XG4gICAgICAgICAgICAgIEdldCBtb3JlIHBob3RvcyBwZXIgcHJvcGVydHkgYW5kIGFkdmFuY2VkIGZlYXR1cmVzXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJwcmVtaXVtXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9wcmljaW5nJztcbiAgICAgICAgICAgICAgICBjbG9zZVNpZGViYXIoKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgVmlldyBQbGFuc1xuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUXVpY2sgU3RhdHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHAtNCByb3VuZGVkLWxnIGJnLXNwYWNlLTgwMC8zMCBib3JkZXIgYm9yZGVyLXNwYWNlLTcwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItM1wiPlxuICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1zcGFjZS00MDBcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtc3BhY2UtMzAwXCI+UXVpY2sgU3RhdHM8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc3BhY2UtNDAwXCI+VGhpcyBNb250aDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVncm91bmQgZm9udC1tZWRpdW1cIj4wIHBob3Rvczwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc3BhY2UtNDAwXCI+UHJvcGVydGllczwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVncm91bmQgZm9udC1tZWRpdW1cIj4wPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2lnbiBPdXQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci10IGJvcmRlci1zcGFjZS03MDBcIj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2lnbk91dH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0IHRleHQtc3BhY2UtNDAwIGhvdmVyOnRleHQtZm9yZWdyb3VuZCBob3ZlcjpiZy1zcGFjZS04MDAvNTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0zXCIgLz5cbiAgICAgICAgICAgIFNpZ24gT3V0XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9uYXY+XG4gICAgPC8+XG4gICk7XG59ICJdLCJuYW1lcyI6WyJ1c2VQYXRobmFtZSIsIkxpbmsiLCJMYXlvdXREYXNoYm9hcmQiLCJCdWlsZGluZzIiLCJDYW1lcmEiLCJTZXR0aW5ncyIsIkNyb3duIiwiQmFyQ2hhcnQzIiwiTG9nT3V0IiwiWCIsIkJ1dHRvbiIsImNyZWF0ZUNsaWVudENvbXBvbmVudENsaWVudCIsInVzZVN0b3JlIiwiRGFzaGJvYXJkTmF2Iiwic2lkZWJhck9wZW4iLCJzZXRTaWRlYmFyT3BlbiIsInBhdGhuYW1lIiwic3VwYWJhc2UiLCJjbGVhclVzZXIiLCJuYXZJdGVtcyIsIm5hbWUiLCJocmVmIiwiaWNvbiIsImRlc2NyaXB0aW9uIiwiaGFuZGxlU2lnbk91dCIsImF1dGgiLCJzaWduT3V0Iiwid2luZG93IiwibG9jYXRpb24iLCJlcnJvciIsImNvbnNvbGUiLCJjbG9zZVNpZGViYXIiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwibmF2IiwidmFyaWFudCIsInNpemUiLCJzcGFuIiwibWFwIiwiaXRlbSIsImlzQWN0aXZlIiwic3RhcnRzV2l0aCIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-royal-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none hover:scale-105 active:scale-95\", {\n    variants: {\n        variant: {\n            default: \"bg-royal-500 text-white hover:bg-royal-600 shadow-lg hover:shadow-xl\",\n            destructive: \"bg-red-500 text-white hover:bg-red-600 shadow-lg hover:shadow-xl\",\n            outline: \"border border-royal-500 text-royal-500 hover:bg-royal-50 dark:hover:bg-royal-900 hover:shadow-lg\",\n            ghost: \"hover:bg-royal-50 text-royal-500 dark:hover:bg-royal-900\",\n            link: \"text-royal-500 underline-offset-4 hover:underline\",\n            premium: \"bg-gradient-to-r from-royal-500 to-purple-600 text-white hover:from-royal-600 hover:to-purple-700 shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-11 py-2 px-6\",\n            sm: \"h-9 px-4\",\n            lg: \"h-12 px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, size, isLoading, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props,\n        disabled: isLoading || props.disabled,\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Processing...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 49,\n            columnNumber: 11\n        }, undefined) : children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 42,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardContent,CardFooter auto */ \n\n\nconst Card = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = \"default\", isHoverable = true, children, ...props }, ref)=>{\n    const baseStyles = \"rounded-xl backdrop-blur-sm transition-all duration-300\";\n    const variants = {\n        default: \"bg-space-800/30 border border-space-700\",\n        premium: \"bg-gradient-to-br from-royal-500/10 to-purple-600/10 border border-royal-500/20\",\n        ghost: \"bg-space-800/10 border border-space-700/50\"\n    };\n    const hoverStyles = isHoverable ? \"hover:-translate-y-1 hover:shadow-2xl\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, variants[variant], hoverStyles, className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 23,\n        columnNumber: 7\n    }, undefined);\n});\nconst CardHeader = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined));\nconst CardContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined));\nconst CardFooter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nCardHeader.displayName = \"CardHeader\";\nCardContent.displayName = \"CardContent\";\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useImageProcessing.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useImageProcessing.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useImageProcessing: () => (/* binding */ useImageProcessing)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n\n\nfunction useImageProcessing() {\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [queue, setQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [currentProcessingIndex, setCurrentProcessingIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\n    const { setIsProcessing: setGlobalProcessing } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)();\n    const processImage = async ({ propertyId, operation, roomType, designStyle, image, forceReprocess })=>{\n        try {\n            setError(null);\n            // Validate image file\n            if (!image || !image.type.startsWith(\"image/\")) {\n                throw new Error(\"Please select a valid image file\");\n            }\n            // Check file size (max 10MB)\n            const maxSize = 10 * 1024 * 1024; // 10MB\n            if (image.size > maxSize) {\n                throw new Error(\"Image file size must be less than 10MB\");\n            }\n            const formData = new FormData();\n            formData.append(\"propertyId\", propertyId);\n            formData.append(\"operation\", operation);\n            formData.append(\"roomType\", roomType);\n            if (designStyle) {\n                formData.append(\"designStyle\", designStyle);\n            }\n            if (forceReprocess) {\n                formData.append(\"forceReprocess\", \"true\");\n            }\n            formData.append(\"image\", image);\n            const response = await fetch(\"/api/process\", {\n                method: \"POST\",\n                body: formData\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                // Handle duplicate detection with reprocess option\n                if (response.status === 409 && data.canReprocess) {\n                    throw new Error(`${data.error}\\n\\n${data.suggestion}`);\n                }\n                throw new Error(data.error || \"Failed to process image\");\n            }\n            // Handle successful processing\n            if (data.success) {\n                return {\n                    success: true,\n                    jobId: data.jobId,\n                    outputImageUrl: data.outputImageUrl,\n                    photosConsumed: data.photosConsumed\n                };\n            } else {\n                throw new Error(\"Image processing failed\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"An error occurred\";\n            setError(errorMessage);\n            throw new Error(errorMessage);\n        }\n    };\n    const processQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ({ propertyId, operation, roomType, designStyle, images, forceReprocess })=>{\n        try {\n            setIsProcessing(true);\n            setGlobalProcessing(true);\n            setError(null);\n            setCurrentProcessingIndex(-1);\n            // Initialize queue\n            const initialQueue = images.map((file, index)=>({\n                    id: `${Date.now()}-${index}`,\n                    file,\n                    status: \"pending\"\n                }));\n            setQueue(initialQueue);\n            const results = [];\n            // Process each image sequentially\n            for(let i = 0; i < images.length; i++){\n                const item = initialQueue[i];\n                setCurrentProcessingIndex(i);\n                // Update queue item status to processing\n                setQueue((prev)=>prev.map((qItem)=>qItem.id === item.id ? {\n                            ...qItem,\n                            status: \"processing\"\n                        } : qItem));\n                try {\n                    const result = await processImage({\n                        propertyId,\n                        operation,\n                        roomType,\n                        designStyle,\n                        image: item.file,\n                        forceReprocess\n                    });\n                    if (result) {\n                        results.push(result);\n                        // Update queue item status to completed\n                        setQueue((prev)=>prev.map((qItem)=>qItem.id === item.id ? {\n                                    ...qItem,\n                                    status: \"completed\",\n                                    result\n                                } : qItem));\n                    } else {\n                        throw new Error(\"Processing failed\");\n                    }\n                } catch (err) {\n                    const errorMessage = err instanceof Error ? err.message : \"Processing failed\";\n                    // Update queue item status to failed\n                    setQueue((prev)=>prev.map((qItem)=>qItem.id === item.id ? {\n                                ...qItem,\n                                status: \"failed\",\n                                error: errorMessage\n                            } : qItem));\n                    // Continue processing other images even if one fails\n                    console.error(`Failed to process image ${i + 1}:`, errorMessage);\n                }\n            }\n            setCurrentProcessingIndex(-1);\n            return results;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n            return [];\n        } finally{\n            setIsProcessing(false);\n            setGlobalProcessing(false);\n        }\n    }, []);\n    const clearQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setQueue([]);\n        setCurrentProcessingIndex(-1);\n    }, []);\n    const createProperty = async (address)=>{\n        try {\n            setError(null);\n            // Validate address\n            if (!address || address.trim().length < 5) {\n                throw new Error(\"Please enter a valid property address\");\n            }\n            const response = await fetch(\"/api/properties\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    address: address.trim()\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create property\");\n            }\n            return data.property;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n            return null;\n        }\n    };\n    const getProperties = async ()=>{\n        try {\n            setError(null);\n            const response = await fetch(\"/api/properties\");\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to fetch properties\");\n            }\n            return data.properties || [];\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n            return [];\n        }\n    };\n    const clearError = ()=>{\n        setError(null);\n    };\n    return {\n        processImage,\n        processQueue,\n        createProperty,\n        getProperties,\n        isProcessing,\n        error,\n        queue,\n        currentProcessingIndex,\n        clearQueue,\n        clearError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useImageProcessing.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/index.ts":
/*!********************************!*\
  !*** ./src/lib/utils/index.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   generatePlaceholderPrompt: () => (/* binding */ generatePlaceholderPrompt),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getOperationLabel: () => (/* binding */ getOperationLabel),\n/* harmony export */   getPlanDetails: () => (/* binding */ getPlanDetails),\n/* harmony export */   isValidImageFile: () => (/* binding */ isValidImageFile)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatBytes(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\n}\nfunction getPlanDetails(tier) {\n    const plans = {\n        free: {\n            name: \"Free\",\n            price: 0,\n            features: [\n                \"2 context per property\",\n                \"Basic room types\",\n                \"Standard quality\"\n            ]\n        },\n        starter: {\n            name: \"Starter\",\n            price: 10,\n            features: [\n                \"5 context per property\",\n                \"All room types\",\n                \"Premium quality\",\n                \"Priority support\"\n            ]\n        },\n        pro: {\n            name: \"Pro\",\n            price: 30,\n            features: [\n                \"8 context per property\",\n                \"All room types\",\n                \"Ultra quality\",\n                \"Priority support\",\n                \"API access\"\n            ]\n        },\n        team: {\n            name: \"Team\",\n            price: 99,\n            features: [\n                \"10 context per property\",\n                \"All features\",\n                \"Ultra quality\",\n                \"Dedicated support\",\n                \"API access\",\n                \"Custom branding\"\n            ]\n        }\n    };\n    return plans[tier];\n}\nfunction getOperationLabel(operation) {\n    const labels = {\n        declutter: \"De-Clutter Room\",\n        stage: \"Stage Room\",\n        chain: \"De-Clutter & Stage\"\n    };\n    return labels[operation];\n}\nfunction delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction generatePlaceholderPrompt(roomType) {\n    const prompts = {\n        living: \"Modern living room with comfortable seating...\",\n        bedroom: \"Cozy bedroom with natural light...\",\n        kitchen: \"Contemporary kitchen with clean lines...\",\n        bathroom: \"Elegant bathroom with modern fixtures...\",\n        dining: \"Welcoming dining space with ambient lighting...\",\n        office: \"Professional home office setup...\"\n    };\n    return prompts[roomType] || \"Transform this space...\";\n}\nfunction isValidImageFile(file) {\n    const validTypes = [\n        \"image/jpeg\",\n        \"image/png\",\n        \"image/webp\"\n    ];\n    return validTypes.includes(file.type);\n}\nfunction getErrorMessage(error) {\n    if (error instanceof Error) return error.message;\n    return String(error);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/useStore.ts":
/*!*******************************!*\
  !*** ./src/store/useStore.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contextLimit: () => (/* binding */ contextLimit),\n/* harmony export */   contextUsed: () => (/* binding */ contextUsed),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// Helper function to get photo limits based on plan\nfunction getPlanPhotoLimit(plan) {\n    const limits = {\n        free: 2,\n        starter: 5,\n        pro: 15,\n        team: 50\n    };\n    return limits[plan] || 2;\n}\nconst useStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // User state\n        user: null,\n        setUser: (user)=>set({\n                user\n            }),\n        clearUser: ()=>set({\n                user: null,\n                selectedProperty: null,\n                photosUsed: 0,\n                photoLimit: 0\n            }),\n        // Property state\n        selectedProperty: null,\n        setSelectedProperty: (property)=>{\n            set({\n                selectedProperty: property\n            });\n            if (property) {\n                set({\n                    photosUsed: property.photosUsed,\n                    photoLimit: property.photoLimit\n                });\n            }\n        },\n        // Photo usage state\n        photosUsed: 0,\n        photoLimit: 0,\n        updatePhotoUsage: (used)=>{\n            set({\n                photosUsed: used\n            });\n            const { selectedProperty } = get();\n            if (selectedProperty) {\n                set({\n                    selectedProperty: {\n                        ...selectedProperty,\n                        photosUsed: used\n                    }\n                });\n            }\n        },\n        setPhotoLimits: (used, limit)=>set({\n                photosUsed: used,\n                photoLimit: limit\n            }),\n        // UI state\n        sidebarOpen: false,\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        // Processing state\n        isProcessing: false,\n        setIsProcessing: (processing)=>set({\n                isProcessing: processing\n            }),\n        // Error state\n        error: null,\n        setError: (error)=>set({\n                error\n            }),\n        clearError: ()=>set({\n                error: null\n            })\n    }), {\n    name: \"staiger-store\",\n    partialize: (state)=>({\n            user: state.user,\n            selectedProperty: state.selectedProperty,\n            photosUsed: state.photosUsed,\n            photoLimit: state.photoLimit\n        })\n}));\n// Aliases for backward compatibility with existing code that uses context terminology\nconst contextUsed = (state)=>state.photosUsed;\nconst contextLimit = (state)=>state.photoLimit;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/useStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"31c1960a3620\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGQ4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMxYzE5NjBhMzYyMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/loading.tsx":
/*!***************************************!*\
  !*** ./src/app/dashboard/loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\loading.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/process/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/process/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\process\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staiger AI - Virtual Staging Platform\",\n    description: \"Transform your real estate photos with AI-powered virtual staging and decluttering\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} bg-space-900 text-foreground antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVO2tCQUN4Qiw0RUFBQ0M7WUFBS0QsV0FBVyxDQUFDLEVBQUVSLCtKQUFlLENBQUMseUNBQXlDLENBQUM7c0JBQzNFSzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJTdGFpZ2VyIEFJIC0gVmlydHVhbCBTdGFnaW5nIFBsYXRmb3JtXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlRyYW5zZm9ybSB5b3VyIHJlYWwgZXN0YXRlIHBob3RvcyB3aXRoIEFJLXBvd2VyZWQgdmlydHVhbCBzdGFnaW5nIGFuZCBkZWNsdXR0ZXJpbmdcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gYmctc3BhY2UtOTAwIHRleHQtZm9yZWdyb3VuZCBhbnRpYWxpYXNlZGB9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFpZ2VyLy4vc3JjL2FwcC9mYXZpY29uLmljbz9jZDk0Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/zustand","vendor-chunks/use-sync-external-store","vendor-chunks/prop-types","vendor-chunks/file-selector","vendor-chunks/react-dropzone","vendor-chunks/react-is","vendor-chunks/tslib","vendor-chunks/object-assign","vendor-chunks/attr-accept"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprocess%2Fpage&page=%2Fdashboard%2Fprocess%2Fpage&appPaths=%2Fdashboard%2Fprocess%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprocess%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();