/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/properties/page";
exports.ids = ["app/dashboard/properties/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fproperties%2Fpage&page=%2Fdashboard%2Fproperties%2Fpage&appPaths=%2Fdashboard%2Fproperties%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fproperties%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fproperties%2Fpage&page=%2Fdashboard%2Fproperties%2Fpage&appPaths=%2Fdashboard%2Fproperties%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fproperties%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'properties',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/properties/page.tsx */ \"(rsc)/./src/app/dashboard/properties/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/loading.tsx */ \"(rsc)/./src/app/dashboard/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/properties/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/properties/page\",\n        pathname: \"/dashboard/properties\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZkYXNoYm9hcmQlMkZwcm9wZXJ0aWVzJTJGcGFnZSZwYWdlPSUyRmRhc2hib2FyZCUyRnByb3BlcnRpZXMlMkZwYWdlJmFwcFBhdGhzPSUyRmRhc2hib2FyZCUyRnByb3BlcnRpZXMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGFzaGJvYXJkJTJGcHJvcGVydGllcyUyRnBhZ2UudHN4JmFwcERpcj1DJTNBJTVDVXNlcnMlNUNQcm9qZWN0MkRyZWFtJTVDRGVza3RvcCU1Q3N0YWlnZXIlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q1Byb2plY3QyRHJlYW0lNUNEZXNrdG9wJTVDc3RhaWdlciZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsMExBQTBIO0FBQ2pKO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLHdLQUFnSDtBQUN6SSxrQkFBa0IsMEtBQWlIO0FBQ25JO0FBQ0Esb0NBQW9DLHNmQUE2UDtBQUNqUztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBcUc7QUFDOUgsb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBLG9DQUFvQyxzZkFBNlA7QUFDalM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvPzQ4MDEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZGFzaGJvYXJkJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAncHJvcGVydGllcycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFByb2plY3QyRHJlYW1cXFxcRGVza3RvcFxcXFxzdGFpZ2VyXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHByb3BlcnRpZXNcXFxccGFnZS50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcUHJvamVjdDJEcmVhbVxcXFxEZXNrdG9wXFxcXHN0YWlnZXJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccHJvcGVydGllc1xcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUHJvamVjdDJEcmVhbVxcXFxEZXNrdG9wXFxcXHN0YWlnZXJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxQcm9qZWN0MkRyZWFtXFxcXERlc2t0b3BcXFxcc3RhaWdlclxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCJdLFxuJ2xvYWRpbmcnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcm9qZWN0MkRyZWFtXFxcXERlc2t0b3BcXFxcc3RhaWdlclxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsb2FkaW5nLnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxQcm9qZWN0MkRyZWFtXFxcXERlc2t0b3BcXFxcc3RhaWdlclxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsb2FkaW5nLnRzeFwiXSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUM6XFxcXFVzZXJzXFxcXFByb2plY3QyRHJlYW1cXFxcRGVza3RvcFxcXFxzdGFpZ2VyXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcm9qZWN0MkRyZWFtXFxcXERlc2t0b3BcXFxcc3RhaWdlclxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcUHJvamVjdDJEcmVhbVxcXFxEZXNrdG9wXFxcXHN0YWlnZXJcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUM6XFxcXFVzZXJzXFxcXFByb2plY3QyRHJlYW1cXFxcRGVza3RvcFxcXFxzdGFpZ2VyXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxQcm9qZWN0MkRyZWFtXFxcXERlc2t0b3BcXFxcc3RhaWdlclxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwcm9wZXJ0aWVzXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvZGFzaGJvYXJkL3Byb3BlcnRpZXMvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9kYXNoYm9hcmQvcHJvcGVydGllcy9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9kYXNoYm9hcmQvcHJvcGVydGllc1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fproperties%2Fpage&page=%2Fdashboard%2Fproperties%2Fpage&appPaths=%2Fdashboard%2Fproperties%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fproperties%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFpZ2VyLz9lNzQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUHJvamVjdDJEcmVhbVxcXFxEZXNrdG9wXFxcXHN0YWlnZXJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/loading.tsx */ \"(ssr)/./src/app/dashboard/loading.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDbG9hZGluZy50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8/N2IyOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFByb2plY3QyRHJlYW1cXFxcRGVza3RvcFxcXFxzdGFpZ2VyXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxvYWRpbmcudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cloading.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cproperties%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cproperties%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/properties/page.tsx */ \"(ssr)/./src/app/dashboard/properties/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDUHJvamVjdDJEcmVhbSU1Q0Rlc2t0b3AlNUNzdGFpZ2VyJTVDc3JjJTVDYXBwJTVDZGFzaGJvYXJkJTVDcHJvcGVydGllcyU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvP2ZjZTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcm9qZWN0MkRyZWFtXFxcXERlc2t0b3BcXFxcc3RhaWdlclxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwcm9wZXJ0aWVzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp%5Cdashboard%5Cproperties%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_dashboard_DashboardNav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardNav */ \"(ssr)/./src/components/dashboard/DashboardNav.tsx\");\n/* harmony import */ var _components_ContextBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ContextBar */ \"(ssr)/./src/components/ContextBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getUser = async ()=>{\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                window.location.href = \"/login\";\n                return;\n            }\n            setUser(user);\n            setLoading(false);\n        };\n        getUser();\n    }, [\n        supabase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 w-8 animate-spin rounded-full border-2 border-royal-500 border-t-transparent\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-space-900 text-foreground\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardNav__WEBPACK_IMPORTED_MODULE_3__.DashboardNav, {\n                sidebarOpen: sidebarOpen,\n                setSidebarOpen: setSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContextBar__WEBPACK_IMPORTED_MODULE_4__.ContextBar, {\n                        sidebarOpen: sidebarOpen,\n                        setSidebarOpen: setSidebarOpen\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto p-4 md:p-6 animate-fade-up\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/loading.tsx":
/*!***************************************!*\
  !*** ./src/app/dashboard/loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-4 animate-fade-up\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-space-400\",\n                    children: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRWUsU0FBU0E7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDQztvQkFBRUQsV0FBVTs4QkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8uL3NyYy9hcHAvZGFzaGJvYXJkL2xvYWRpbmcudHN4PzQ3ODYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbWluLWgtc2NyZWVuIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNCBhbmltYXRlLWZhZGUtdXBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIGFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgYm9yZGVyLTQgYm9yZGVyLXJveWFsLTUwMCBib3JkZXItdC10cmFuc3BhcmVudFwiPjwvZGl2PlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc3BhY2UtNDAwXCI+TG9hZGluZyB5b3VyIGRhc2hib2FyZC4uLjwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59ICJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/properties/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/properties/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Calendar,Camera,MapPin,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Calendar,Camera,MapPin,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Calendar,Camera,MapPin,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Calendar,Camera,MapPin,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Calendar,Camera,MapPin,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_context_engine_ContextEngine__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/context-engine/ContextEngine */ \"(ssr)/./src/services/context-engine/ContextEngine.ts\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction PropertiesPage() {\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newAddress, setNewAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newPropertyType, setNewPropertyType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"residential\");\n    const [addingProperty, setAddingProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const { user, setSelectedProperty } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_7__.useStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadProperties();\n    }, []);\n    const loadProperties = async ()=>{\n        try {\n            setLoading(true);\n            const { data: { user: authUser } } = await supabase.auth.getUser();\n            if (!authUser) return;\n            const userProperties = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.getUserProperties)(authUser.id);\n            setProperties(userProperties);\n        } catch (error) {\n            console.error(\"Error loading properties:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddProperty = async (e)=>{\n        e.preventDefault();\n        if (!newAddress.trim()) return;\n        try {\n            setAddingProperty(true);\n            setError(null);\n            const { data: { user: authUser } } = await supabase.auth.getUser();\n            if (!authUser) return;\n            // Ensure user profile exists, create if not\n            let userProfile = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.getUserProfile)(authUser.id);\n            if (!userProfile) {\n                // Create user profile if it doesn't exist\n                const { error: createError } = await supabase.from(\"users\").insert({\n                    id: authUser.id,\n                    email: authUser.email || \"\",\n                    full_name: authUser.user_metadata?.full_name || \"\",\n                    plan: \"free\",\n                    created_at: new Date().toISOString(),\n                    last_login: new Date().toISOString()\n                });\n                if (createError) {\n                    throw new Error(\"Failed to create user profile\");\n                }\n                // Fetch the newly created profile\n                userProfile = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.getUserProfile)(authUser.id);\n            }\n            const plan = userProfile?.plan || \"free\";\n            // Calculate property index and photo limit\n            const propertyIndex = properties.length;\n            const photoLimit = _services_context_engine_ContextEngine__WEBPACK_IMPORTED_MODULE_6__.ContextEngine.getPhotoLimit(plan, propertyIndex);\n            // Validate address (basic validation)\n            if (!isValidAddress(newAddress)) {\n                throw new Error(\"Please enter a valid street address\");\n            }\n            // Check for duplicate addresses (abuse prevention)\n            const duplicateAddress = properties.find((p)=>p.address.toLowerCase() === newAddress.toLowerCase());\n            if (duplicateAddress) {\n                throw new Error(\"This address has already been added\");\n            }\n            // Rate limiting check - max 3 properties per hour\n            const recentProperties = properties.filter((p)=>{\n                const createdAt = new Date(p.created_at);\n                const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\n                return createdAt > oneHourAgo;\n            });\n            if (recentProperties.length >= 3) {\n                throw new Error(\"Rate limit exceeded. Please wait before adding more properties.\");\n            }\n            const newProperty = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.createProperty)(authUser.id, newAddress, newPropertyType, photoLimit, propertyIndex);\n            if (newProperty) {\n                setProperties([\n                    newProperty,\n                    ...properties\n                ]);\n                setNewAddress(\"\");\n                setNewPropertyType(\"residential\");\n                setShowAddForm(false);\n            }\n        } catch (error) {\n            setError(error instanceof Error ? error.message : \"Failed to add property\");\n        } finally{\n            setAddingProperty(false);\n        }\n    };\n    const isValidAddress = (address)=>{\n        // Basic address validation\n        const addressRegex = /^\\d+\\s+[A-Za-z0-9\\s,.-]+$/;\n        return addressRegex.test(address.trim()) && address.length >= 10;\n    };\n    const handleSelectProperty = (property)=>{\n        setSelectedProperty({\n            id: property.id,\n            address: property.address,\n            type: property.property_type,\n            photosUsed: property.photos_used,\n            photoLimit: property.photo_limit,\n            createdAt: property.created_at\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4 animate-fade-up\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 mx-auto animate-spin rounded-full border-4 border-royal-500 border-t-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-space-400\",\n                        children: \"Loading your properties...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 animate-fade-up\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Properties\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-space-400\",\n                                children: \"Manage your real estate properties\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>setShowAddForm(true),\n                        variant: \"premium\",\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Add Property\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"animate-fade-up\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium\",\n                            children: \"Add New Property\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleAddProperty,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-foreground mb-2\",\n                                            children: \"Property Address *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newAddress,\n                                            onChange: (e)=>setNewAddress(e.target.value),\n                                            placeholder: \"123 Main Street, City, State\",\n                                            className: \"w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground placeholder:text-space-400 transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-space-500 mt-1\",\n                                            children: \"Enter a valid street address (e.g., 123 Main St, Anytown, CA)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-foreground mb-2\",\n                                            children: \"Property Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newPropertyType,\n                                            onChange: (e)=>setNewPropertyType(e.target.value),\n                                            className: \"w-full rounded-lg border border-space-700 bg-space-800/50 py-3 px-4 text-foreground transition-all duration-200 focus:border-royal-500 focus:outline-none focus:ring-2 focus:ring-royal-500/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"residential\",\n                                                    children: \"Residential\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"commercial\",\n                                                    children: \"Commercial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"condo\",\n                                                    children: \"Condo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"townhouse\",\n                                                    children: \"Townhouse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"apartment\",\n                                                    children: \"Apartment\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-fade-in rounded-lg bg-red-500/10 border border-red-500/20 p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-400\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            variant: \"premium\",\n                                            isLoading: addingProperty,\n                                            disabled: !newAddress.trim(),\n                                            children: \"Add Property\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"ghost\",\n                                            onClick: ()=>{\n                                                setShowAddForm(false);\n                                                setError(null);\n                                                setNewAddress(\"\");\n                                                setNewPropertyType(\"residential\");\n                                            },\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this),\n            properties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 w-16 mx-auto rounded-full bg-royal-500/10 flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-8 w-8 text-royal-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-foreground mb-2\",\n                            children: \"No properties yet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-space-400 mb-6\",\n                            children: \"Add your first property to start transforming photos with AI\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            variant: \"premium\",\n                            children: \"Add Your First Property\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                children: properties.map((property, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"cursor-pointer hover:scale-105 transition-all duration-200\",\n                        style: {\n                            animationDelay: `${index * 0.1}s`\n                        },\n                        onClick: ()=>handleSelectProperty(property),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-royal-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-foreground capitalize\",\n                                                    children: property.property_type\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1 text-xs text-space-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        property.photos_used,\n                                                        \"/\",\n                                                        property.photo_limit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 text-space-400 mt-0.5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-space-300 line-clamp-2\",\n                                                children: property.address\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-xs text-space-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Calendar_Camera_MapPin_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: new Date(property.created_at).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Property #\",\n                                                    property.property_index + 1\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-space-400\",\n                                                        children: \"Photo Usage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: property.photos_used >= property.photo_limit ? \"text-red-400\" : \"text-space-400\",\n                                                        children: [\n                                                            property.photos_used,\n                                                            \"/\",\n                                                            property.photo_limit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-2 bg-space-700 rounded-full overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `h-full transition-all duration-500 rounded-full ${property.photos_used >= property.photo_limit ? \"bg-gradient-to-r from-red-500 to-red-600\" : \"bg-gradient-to-r from-royal-500 to-purple-600\"}`,\n                                                    style: {\n                                                        width: `${Math.min(property.photos_used / property.photo_limit * 100, 100)}%`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            handleSelectProperty(property);\n                                            // Navigate to processing page\n                                            window.location.href = \"/dashboard/process\";\n                                        },\n                                        children: \"Process Photos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, property.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/properties/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ContextBar.tsx":
/*!***************************************!*\
  !*** ./src/components/ContextBar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextBar: () => (/* binding */ ContextBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crown,Menu,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContextBar auto */ \n\n\n\n\nfunction ContextBar({ sidebarOpen, setSidebarOpen }) {\n    const photosUsed = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.photosUsed);\n    const photoLimit = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.photoLimit);\n    const plan = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.plan);\n    const user = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)((state)=>state.user);\n    // Ensure we show correct values even if user isn't in database yet\n    const currentPlan = user?.plan || plan || \"free\";\n    const currentLimit = photoLimit || 2; // Default to free plan limit\n    const currentUsed = photosUsed || 0;\n    const percentage = currentUsed / currentLimit * 100;\n    const isNearLimit = percentage > 80;\n    const remaining = currentLimit - currentUsed;\n    const planIcons = {\n        free: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        starter: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        pro: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        team: _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    const PlanIcon = planIcons[currentPlan] || _barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-b border-space-800 bg-space-900/50 px-4 md:px-6 py-3 animate-slide-in\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            className: \"lg:hidden h-8 w-8 p-0 text-space-400 hover:text-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crown_Menu_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanIcon, {\n                                    className: \"h-4 w-4 text-royal-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-foreground capitalize\",\n                                    children: [\n                                        currentPlan,\n                                        \" Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 md:space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs md:text-sm text-space-400 hidden sm:inline\",\n                                    children: \"Photos remaining:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs md:text-sm text-space-400 sm:hidden\",\n                                    children: \"Photos:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs md:text-sm font-medium\", isNearLimit ? \"text-yellow-400\" : \"text-foreground\"),\n                                    children: [\n                                        remaining,\n                                        \"/\",\n                                        currentLimit\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:block w-20 md:w-32 h-2 rounded-full p-0.5 animate-gradient-flow\",\n                            style: {\n                                background: isNearLimit ? \"linear-gradient(45deg, #eab308, #ef4444, #f97316, #eab308, #ef4444)\" : \"linear-gradient(45deg, #0066ff, #9333ea, #7c3aed, #0066ff, #9333ea)\",\n                                backgroundSize: \"300% 300%\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-full bg-space-700 rounded-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-full transition-all duration-700 ease-out rounded-full transform origin-left\", isNearLimit ? \"bg-gradient-to-r from-yellow-500 to-red-500\" : \"bg-gradient-to-r from-royal-500 to-purple-600\", percentage > 0 && \"animate-scale-in-x\"),\n                                    style: {\n                                        width: `${Math.min(percentage, 100)}%`,\n                                        animationDelay: \"0.3s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        isNearLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse-glow rounded-lg bg-yellow-500/10 px-2 py-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-yellow-400\",\n                                children: \"Low\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ContextBar.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ContextBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardNav.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/DashboardNav.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardNav: () => (/* binding */ DashboardNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Camera,Crown,LayoutDashboard,LogOut,Settings,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardNav auto */ \n\n\n\n\n\n\nfunction DashboardNav({ sidebarOpen, setSidebarOpen }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__.createClientComponentClient)();\n    const { clearUser } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    const navItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Overview and analytics\"\n        },\n        {\n            name: \"Properties\",\n            href: \"/dashboard/properties\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Manage your properties\"\n        },\n        {\n            name: \"Process Photos\",\n            href: \"/dashboard/process\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"AI virtual staging\"\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/settings\",\n            icon: _barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Account preferences\"\n        }\n    ];\n    const handleSignOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            clearUser();\n            window.location.href = \"/login\";\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const closeSidebar = ()=>setSidebarOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\",\n                onClick: closeSidebar\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-space-900/95 lg:bg-space-900/50 backdrop-blur-sm border-r border-space-700 flex flex-col\n        transform transition-transform duration-300 ease-in-out lg:transform-none\n        ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden absolute top-4 right-4 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: closeSidebar,\n                            className: \"h-8 w-8 p-0 text-space-400 hover:text-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-space-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard\",\n                            className: \"flex items-center space-x-3\",\n                            onClick: closeSidebar,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gradient-to-br from-royal-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-foreground\",\n                                    children: \"Staiger AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 px-4 py-6 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: navItems.map((item)=>{\n                                    const isActive = pathname === item.href || item.href !== \"/dashboard\" && pathname.startsWith(item.href);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        onClick: closeSidebar,\n                                        className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group ${isActive ? \"bg-royal-500/20 text-royal-400 border border-royal-500/30\" : \"text-space-400 hover:text-foreground hover:bg-space-800/50\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: `h-5 w-5 ${isActive ? \"text-royal-400\" : \"text-space-400 group-hover:text-foreground\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `font-medium ${isActive ? \"text-royal-400\" : \"text-space-300 group-hover:text-foreground\"}`,\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-space-500 group-hover:text-space-400\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-4 rounded-lg bg-gradient-to-br from-royal-500/10 to-purple-600/10 border border-royal-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 text-royal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-royal-400\",\n                                                children: \"Upgrade Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-space-400 mb-3\",\n                                        children: \"Get more photos per property and advanced features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"premium\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        onClick: ()=>{\n                                            window.location.href = \"/pricing\";\n                                            closeSidebar();\n                                        },\n                                        children: \"View Plans\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 rounded-lg bg-space-800/30 border border-space-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-space-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-space-300\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-space-400\",\n                                                        children: \"This Month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-foreground font-medium\",\n                                                        children: \"0 photos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-space-400\",\n                                                        children: \"Properties\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-foreground font-medium\",\n                                                        children: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-space-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleSignOut,\n                            className: \"w-full justify-start text-space-400 hover:text-foreground hover:bg-space-800/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Camera_Crown_LayoutDashboard_LogOut_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                \"Sign Out\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\dashboard\\\\DashboardNav.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-royal-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none hover:scale-105 active:scale-95\", {\n    variants: {\n        variant: {\n            default: \"bg-royal-500 text-white hover:bg-royal-600 shadow-lg hover:shadow-xl\",\n            destructive: \"bg-red-500 text-white hover:bg-red-600 shadow-lg hover:shadow-xl\",\n            outline: \"border border-royal-500 text-royal-500 hover:bg-royal-50 dark:hover:bg-royal-900 hover:shadow-lg\",\n            ghost: \"hover:bg-royal-50 text-royal-500 dark:hover:bg-royal-900\",\n            link: \"text-royal-500 underline-offset-4 hover:underline\",\n            premium: \"bg-gradient-to-r from-royal-500 to-purple-600 text-white hover:from-royal-600 hover:to-purple-700 shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-11 py-2 px-6\",\n            sm: \"h-9 px-4\",\n            lg: \"h-12 px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, size, isLoading, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props,\n        disabled: isLoading || props.disabled,\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Processing...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 49,\n            columnNumber: 11\n        }, undefined) : children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 42,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9CdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUV5RDtBQUN4QjtBQUM0QjtBQUU3RCxNQUFNRyxpQkFBaUJELDZEQUFHQSxDQUN4QixnU0FDQTtJQUNFRSxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLFNBQVM7UUFDWDtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNqQixpREFBVUEsQ0FDdkIsQ0FBQyxFQUFFa0IsU0FBUyxFQUFFYixPQUFPLEVBQUVPLElBQUksRUFBRU8sU0FBUyxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUM1RCxxQkFDRSw4REFBQ0M7UUFDQ0wsV0FBV2pCLDhDQUFFQSxDQUFDRSxlQUFlO1lBQUVFO1lBQVNPO1lBQU1NO1FBQVU7UUFDeERJLEtBQUtBO1FBQ0osR0FBR0QsS0FBSztRQUNURyxVQUFVTCxhQUFhRSxNQUFNRyxRQUFRO2tCQUVwQ0wsMEJBQ0MsOERBQUNNO1lBQUlQLFdBQVU7OzhCQUNiLDhEQUFDTztvQkFBSVAsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDUTs4QkFBSzs7Ozs7Ozs7Ozs7d0JBR1JOOzs7Ozs7QUFJUjtBQUdGSCxPQUFPVSxXQUFXLEdBQUc7QUFFYSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvLi9zcmMvY29tcG9uZW50cy91aS9CdXR0b24udHN4PzAxM2EiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgeyBCdXR0b25IVE1MQXR0cmlidXRlcywgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XHJcbmltcG9ydCB7IFZhcmlhbnRQcm9wcywgY3ZhIH0gZnJvbSAnY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5JztcclxuXHJcbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxyXG4gICdpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcm95YWwtNTAwIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgaG92ZXI6c2NhbGUtMTA1IGFjdGl2ZTpzY2FsZS05NScsXHJcbiAge1xyXG4gICAgdmFyaWFudHM6IHtcclxuICAgICAgdmFyaWFudDoge1xyXG4gICAgICAgIGRlZmF1bHQ6ICdiZy1yb3lhbC01MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1yb3lhbC02MDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCcsXHJcbiAgICAgICAgZGVzdHJ1Y3RpdmU6ICdiZy1yZWQtNTAwIHRleHQtd2hpdGUgaG92ZXI6YmctcmVkLTYwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsJyxcclxuICAgICAgICBvdXRsaW5lOiAnYm9yZGVyIGJvcmRlci1yb3lhbC01MDAgdGV4dC1yb3lhbC01MDAgaG92ZXI6Ymctcm95YWwtNTAgZGFyazpob3ZlcjpiZy1yb3lhbC05MDAgaG92ZXI6c2hhZG93LWxnJyxcclxuICAgICAgICBnaG9zdDogJ2hvdmVyOmJnLXJveWFsLTUwIHRleHQtcm95YWwtNTAwIGRhcms6aG92ZXI6Ymctcm95YWwtOTAwJyxcclxuICAgICAgICBsaW5rOiAndGV4dC1yb3lhbC01MDAgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZScsXHJcbiAgICAgICAgcHJlbWl1bTogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1yb3lhbC01MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmZyb20tcm95YWwtNjAwIGhvdmVyOnRvLXB1cnBsZS03MDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCcsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNpemU6IHtcclxuICAgICAgICBkZWZhdWx0OiAnaC0xMSBweS0yIHB4LTYnLFxyXG4gICAgICAgIHNtOiAnaC05IHB4LTQnLFxyXG4gICAgICAgIGxnOiAnaC0xMiBweC04JyxcclxuICAgICAgICBpY29uOiAnaC0xMCB3LTEwJyxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcclxuICAgICAgdmFyaWFudDogJ2RlZmF1bHQnLFxyXG4gICAgICBzaXplOiAnZGVmYXVsdCcsXHJcbiAgICB9LFxyXG4gIH1cclxuKTtcclxuXHJcbmludGVyZmFjZSBCdXR0b25Qcm9wc1xyXG4gIGV4dGVuZHMgQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+LFxyXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xyXG4gIGlzTG9hZGluZz86IGJvb2xlYW47XHJcbn1cclxuXHJcbmNvbnN0IEJ1dHRvbiA9IGZvcndhcmRSZWY8SFRNTEJ1dHRvbkVsZW1lbnQsIEJ1dHRvblByb3BzPihcclxuICAoeyBjbGFzc05hbWUsIHZhcmlhbnQsIHNpemUsIGlzTG9hZGluZywgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4ge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGJ1dHRvblxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxyXG4gICAgICAgIHJlZj17cmVmfVxyXG4gICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nIHx8IHByb3BzLmRpc2FibGVkfVxyXG4gICAgICA+XHJcbiAgICAgICAge2lzTG9hZGluZyA/IChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudFwiIC8+XHJcbiAgICAgICAgICAgIDxzcGFuPlByb2Nlc3NpbmcuLi48L3NwYW4+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApIDogKFxyXG4gICAgICAgICAgY2hpbGRyZW5cclxuICAgICAgICApfVxyXG4gICAgICA8L2J1dHRvbj5cclxuICAgICk7XHJcbiAgfVxyXG4pO1xyXG5cclxuQnV0dG9uLmRpc3BsYXlOYW1lID0gJ0J1dHRvbic7XHJcblxyXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH07ICJdLCJuYW1lcyI6WyJmb3J3YXJkUmVmIiwiY24iLCJjdmEiLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJnaG9zdCIsImxpbmsiLCJwcmVtaXVtIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiY2xhc3NOYW1lIiwiaXNMb2FkaW5nIiwiY2hpbGRyZW4iLCJwcm9wcyIsInJlZiIsImJ1dHRvbiIsImRpc2FibGVkIiwiZGl2Iiwic3BhbiIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardContent,CardFooter auto */ \n\n\nconst Card = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = \"default\", isHoverable = true, children, ...props }, ref)=>{\n    const baseStyles = \"rounded-xl backdrop-blur-sm transition-all duration-300\";\n    const variants = {\n        default: \"bg-space-800/30 border border-space-700\",\n        premium: \"bg-gradient-to-br from-royal-500/10 to-purple-600/10 border border-royal-500/20\",\n        ghost: \"bg-space-800/10 border border-space-700/50\"\n    };\n    const hoverStyles = isHoverable ? \"hover:-translate-y-1 hover:shadow-2xl\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, variants[variant], hoverStyles, className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 23,\n        columnNumber: 7\n    }, undefined);\n});\nconst CardHeader = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined));\nconst CardContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined));\nconst CardFooter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nCardHeader.displayName = \"CardHeader\";\nCardContent.displayName = \"CardContent\";\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkImageFingerprint: () => (/* binding */ checkImageFingerprint),\n/* harmony export */   createProcessingJob: () => (/* binding */ createProcessingJob),\n/* harmony export */   createProperty: () => (/* binding */ createProperty),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getImageUrl: () => (/* binding */ getImageUrl),\n/* harmony export */   getUserProcessingJobs: () => (/* binding */ getUserProcessingJobs),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserProperties: () => (/* binding */ getUserProperties),\n/* harmony export */   getUserUsageStats: () => (/* binding */ getUserUsageStats),\n/* harmony export */   logUsage: () => (/* binding */ logUsage),\n/* harmony export */   storeImageFingerprint: () => (/* binding */ storeImageFingerprint),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   updateJobStatus: () => (/* binding */ updateJobStatus),\n/* harmony export */   updateProcessingJob: () => (/* binding */ updateProcessingJob),\n/* harmony export */   updatePropertyUsage: () => (/* binding */ updatePropertyUsage),\n/* harmony export */   updateUserProfile: () => (/* binding */ updateUserProfile),\n/* harmony export */   uploadImage: () => (/* binding */ uploadImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// User Profile Functions\nasync function getUserProfile(userId) {\n    const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n    if (error) {\n        console.error(\"Error fetching user profile:\", error);\n        return null;\n    }\n    return data;\n}\nasync function updateUserProfile(userId, updates) {\n    const { data, error } = await supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n    if (error) {\n        console.error(\"Error updating user profile:\", error);\n        throw error;\n    }\n    return data;\n}\n// Property Functions\nasync function getUserProperties(userId) {\n    const { data, error } = await supabase.from(\"properties\").select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n        ascending: false\n    });\n    if (error) {\n        console.error(\"Error fetching properties:\", error);\n        return [];\n    }\n    return data || [];\n}\nasync function createProperty(userId, address, propertyType, photoLimit, propertyIndex) {\n    const { data, error } = await supabase.from(\"properties\").insert({\n        user_id: userId,\n        address,\n        property_type: propertyType,\n        photo_limit: photoLimit,\n        property_index: propertyIndex,\n        photos_used: 0\n    }).select().single();\n    if (error) {\n        console.error(\"Error creating property:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function updatePropertyUsage(propertyId, photosUsed) {\n    const { data, error } = await supabase.from(\"properties\").update({\n        photos_used: photosUsed\n    }).eq(\"id\", propertyId).select().single();\n    if (error) {\n        console.error(\"Error updating property usage:\", error);\n        throw error;\n    }\n    return data;\n}\n// Processing Job Functions\nasync function createProcessingJob(userId, propertyId, operationType, photoCost, inputImageUrl, metadata = {}) {\n    const { data, error } = await supabase.from(\"processing_jobs\").insert({\n        user_id: userId,\n        property_id: propertyId,\n        operation_type: operationType,\n        photo_cost: photoCost,\n        input_image_url: inputImageUrl,\n        status: \"pending\",\n        metadata\n    }).select().single();\n    if (error) {\n        console.error(\"Error creating processing job:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function updateProcessingJob(jobId, updates) {\n    const { data, error } = await supabase.from(\"processing_jobs\").update(updates).eq(\"id\", jobId).select().single();\n    if (error) {\n        console.error(\"Error updating processing job:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function getUserProcessingJobs(userId, limit = 10) {\n    const { data, error } = await supabase.from(\"processing_jobs\").select(`\n      *,\n      properties (\n        address,\n        property_type\n      )\n    `).eq(\"user_id\", userId).order(\"created_at\", {\n        ascending: false\n    }).limit(limit);\n    if (error) {\n        console.error(\"Error fetching processing jobs:\", error);\n        return [];\n    }\n    return data || [];\n}\n// Usage Analytics Functions\nasync function getUserUsageStats(userId) {\n    try {\n        // Get total properties\n        const { count: totalProperties } = await supabase.from(\"properties\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"user_id\", userId);\n        // Get total photos processed\n        const { count: totalPhotosProcessed } = await supabase.from(\"processing_jobs\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"user_id\", userId).eq(\"status\", \"completed\");\n        // Get this month's usage\n        const startOfMonth = new Date();\n        startOfMonth.setDate(1);\n        startOfMonth.setHours(0, 0, 0, 0);\n        const { count: thisMonthUsage } = await supabase.from(\"processing_jobs\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"user_id\", userId).eq(\"status\", \"completed\").gte(\"created_at\", startOfMonth.toISOString());\n        // Get recent activity\n        const { data: recentActivity } = await supabase.from(\"processing_jobs\").select(`\n        *,\n        properties (\n          address\n        )\n      `).eq(\"user_id\", userId).order(\"created_at\", {\n            ascending: false\n        }).limit(5);\n        return {\n            totalProperties: totalProperties || 0,\n            totalPhotosProcessed: totalPhotosProcessed || 0,\n            thisMonthUsage: thisMonthUsage || 0,\n            recentActivity: recentActivity || []\n        };\n    } catch (error) {\n        console.error(\"Error fetching usage stats:\", error);\n        return {\n            totalProperties: 0,\n            totalPhotosProcessed: 0,\n            thisMonthUsage: 0,\n            recentActivity: []\n        };\n    }\n}\n// File Upload Functions\nasync function uploadImage(file, bucket, path) {\n    const { data, error } = await supabase.storage.from(bucket).upload(path, file, {\n        cacheControl: \"3600\",\n        upsert: false\n    });\n    if (error) {\n        throw error;\n    }\n    return data;\n}\nasync function getImageUrl(bucket, path) {\n    const { data } = supabase.storage.from(bucket).getPublicUrl(path);\n    return data.publicUrl;\n}\n// Image Fingerprint Functions\nasync function storeImageFingerprint(userId, propertyId, imageHash, imageUrl) {\n    const { data, error } = await supabase.from(\"image_fingerprints\").insert({\n        user_id: userId,\n        property_id: propertyId,\n        image_hash: imageHash,\n        image_url: imageUrl\n    }).select().single();\n    if (error) {\n        console.error(\"Error storing image fingerprint:\", error);\n        throw error;\n    }\n    return data;\n}\nasync function checkImageFingerprint(userId, imageHash) {\n    const { data, error } = await supabase.from(\"image_fingerprints\").select(\"*\").eq(\"user_id\", userId).eq(\"image_hash\", imageHash).single();\n    if (error && error.code !== \"PGRST116\") {\n        console.error(\"Error checking image fingerprint:\", error);\n        throw error;\n    }\n    return data;\n}\n// Usage Logging Functions\nasync function logUsage(userId, propertyId, jobId, photosConsumed, operationType, metadata = {}) {\n    const { data, error } = await supabase.from(\"usage_logs\").insert({\n        user_id: userId,\n        property_id: propertyId,\n        job_id: jobId,\n        photos_consumed: photosConsumed,\n        operation_type: operationType,\n        metadata\n    }).select().single();\n    if (error) {\n        console.error(\"Error logging usage:\", error);\n        throw error;\n    }\n    return data;\n}\n// Job Status Update Function\nasync function updateJobStatus(jobId, status, outputImageUrl) {\n    try {\n        const updateData = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (outputImageUrl) {\n            updateData.output_image_url = outputImageUrl;\n        }\n        const { error } = await supabase.from(\"processing_jobs\").update(updateData).eq(\"id\", jobId);\n        if (error) {\n            console.error(\"Error updating job status:\", error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error updating job status:\", error);\n        return false;\n    }\n}\n// Dashboard Stats Functions\nasync function getDashboardStats(userId) {\n    try {\n        const [usageStats, userProfile] = await Promise.all([\n            getUserUsageStats(userId),\n            getUserProfile(userId)\n        ]);\n        return {\n            ...usageStats,\n            userPlan: userProfile?.plan || \"free\"\n        };\n    } catch (error) {\n        console.error(\"Error fetching dashboard stats:\", error);\n        return {\n            totalProperties: 0,\n            totalPhotosProcessed: 0,\n            thisMonthUsage: 0,\n            recentActivity: [],\n            userPlan: \"free\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/index.ts":
/*!********************************!*\
  !*** ./src/lib/utils/index.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   generatePlaceholderPrompt: () => (/* binding */ generatePlaceholderPrompt),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getOperationLabel: () => (/* binding */ getOperationLabel),\n/* harmony export */   getPlanDetails: () => (/* binding */ getPlanDetails),\n/* harmony export */   isValidImageFile: () => (/* binding */ isValidImageFile)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatBytes(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\n}\nfunction getPlanDetails(tier) {\n    const plans = {\n        free: {\n            name: \"Free\",\n            price: 0,\n            features: [\n                \"2 context per property\",\n                \"Basic room types\",\n                \"Standard quality\"\n            ]\n        },\n        starter: {\n            name: \"Starter\",\n            price: 10,\n            features: [\n                \"5 context per property\",\n                \"All room types\",\n                \"Premium quality\",\n                \"Priority support\"\n            ]\n        },\n        pro: {\n            name: \"Pro\",\n            price: 30,\n            features: [\n                \"8 context per property\",\n                \"All room types\",\n                \"Ultra quality\",\n                \"Priority support\",\n                \"API access\"\n            ]\n        },\n        team: {\n            name: \"Team\",\n            price: 99,\n            features: [\n                \"10 context per property\",\n                \"All features\",\n                \"Ultra quality\",\n                \"Dedicated support\",\n                \"API access\",\n                \"Custom branding\"\n            ]\n        }\n    };\n    return plans[tier];\n}\nfunction getOperationLabel(operation) {\n    const labels = {\n        declutter: \"De-Clutter Room\",\n        stage: \"Stage Room\",\n        chain: \"De-Clutter & Stage\"\n    };\n    return labels[operation];\n}\nfunction delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction generatePlaceholderPrompt(roomType) {\n    const prompts = {\n        living: \"Modern living room with comfortable seating...\",\n        bedroom: \"Cozy bedroom with natural light...\",\n        kitchen: \"Contemporary kitchen with clean lines...\",\n        bathroom: \"Elegant bathroom with modern fixtures...\",\n        dining: \"Welcoming dining space with ambient lighting...\",\n        office: \"Professional home office setup...\"\n    };\n    return prompts[roomType] || \"Transform this space...\";\n}\nfunction isValidImageFile(file) {\n    const validTypes = [\n        \"image/jpeg\",\n        \"image/png\",\n        \"image/webp\"\n    ];\n    return validTypes.includes(file.type);\n}\nfunction getErrorMessage(error) {\n    if (error instanceof Error) return error.message;\n    return String(error);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/context-engine/ContextEngine.ts":
/*!******************************************************!*\
  !*** ./src/services/context-engine/ContextEngine.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextEngine: () => (/* binding */ ContextEngine)\n/* harmony export */ });\nclass ContextEngine {\n    static{\n        this.PLAN_CONFIGS = {\n            free: {\n                name: \"Free\",\n                photosPerProperty: 2,\n                decayAfterProperties: 0,\n                decayAmount: 0,\n                minimumPhotos: 2,\n                features: [\n                    \"Basic staging\",\n                    \"Basic decluttering\",\n                    \"Standard processing\"\n                ]\n            },\n            starter: {\n                name: \"Starter\",\n                photosPerProperty: 5,\n                decayAfterProperties: 10,\n                decayAmount: 1,\n                minimumPhotos: 2,\n                features: [\n                    \"Priority processing\",\n                    \"Advanced staging styles\",\n                    \"Email support\"\n                ]\n            },\n            pro: {\n                name: \"Pro\",\n                photosPerProperty: 15,\n                decayAfterProperties: 20,\n                decayAmount: 1,\n                minimumPhotos: 5,\n                features: [\n                    \"Premium styling options\",\n                    \"Priority support\",\n                    \"Bulk processing\",\n                    \"Advanced decluttering\"\n                ]\n            },\n            team: {\n                name: \"Team\",\n                photosPerProperty: 50,\n                decayAfterProperties: 50,\n                decayAmount: 1,\n                minimumPhotos: 10,\n                features: [\n                    \"API access\",\n                    \"24/7 support\",\n                    \"Custom watermarks\",\n                    \"Team management\",\n                    \"Analytics dashboard\"\n                ]\n            }\n        };\n    }\n    static{\n        // AI AGENTIC OPERATIONS - Each operation represents an AI agent with specific capabilities\n        this.OPERATIONS = {\n            declutter: {\n                type: \"declutter\",\n                photoCost: 1,\n                description: \"AI Declutter Agent: Intelligently removes clutter and unwanted items while preserving room structure\",\n                agentRole: \"Specialized in identifying and removing clutter while maintaining architectural integrity\"\n            },\n            staging: {\n                type: \"staging\",\n                photoCost: 1,\n                description: \"AI Staging Agent: Adds contextually appropriate furniture and decor based on room type and style preferences\",\n                agentRole: \"Expert interior designer that selects and places furniture to maximize appeal\"\n            },\n            chain: {\n                type: \"chain\",\n                photoCost: 2,\n                description: \"AI Chain Agent: Complete transformation using declutter agent first, then staging agent\",\n                agentRole: \"Master coordinator that orchestrates multiple AI agents for complete room transformation\"\n            }\n        };\n    }\n    static getPhotoLimit(plan, propertyIndex) {\n        const config = this.PLAN_CONFIGS[plan.toLowerCase()];\n        if (!config) return this.PLAN_CONFIGS.free.photosPerProperty;\n        if (config.decayAfterProperties === 0) {\n            return config.photosPerProperty;\n        }\n        const decaySteps = Math.floor(propertyIndex / config.decayAfterProperties);\n        const currentLimit = config.photosPerProperty - decaySteps * config.decayAmount;\n        return Math.max(currentLimit, config.minimumPhotos);\n    }\n    static getOperationCost(operationType) {\n        const operation = this.OPERATIONS[operationType.toLowerCase()];\n        return operation ? operation.photoCost : 1;\n    }\n    static getOperationDetails(operationType) {\n        return this.OPERATIONS[operationType.toLowerCase()] || null;\n    }\n    static canProcessPhoto(plan, propertyIndex, currentUsage, operationType) {\n        const photoLimit = this.getPhotoLimit(plan, propertyIndex);\n        const operationCost = this.getOperationCost(operationType);\n        const photosRemaining = photoLimit - currentUsage;\n        if (photosRemaining < operationCost) {\n            return {\n                canProcess: false,\n                reason: `Not enough photos remaining. Need ${operationCost}, have ${photosRemaining}`,\n                photosRemaining\n            };\n        }\n        return {\n            canProcess: true,\n            photosRemaining: photosRemaining - operationCost\n        };\n    }\n    static getPlanFeatures(plan) {\n        const config = this.PLAN_CONFIGS[plan.toLowerCase()];\n        return config ? config.features : this.PLAN_CONFIGS.free.features;\n    }\n    static getAllPlans() {\n        return Object.values(this.PLAN_CONFIGS);\n    }\n    static getRecommendedPlan(averagePhotosPerProperty) {\n        if (averagePhotosPerProperty <= 2) return \"free\";\n        if (averagePhotosPerProperty <= 5) return \"starter\";\n        if (averagePhotosPerProperty <= 15) return \"pro\";\n        return \"team\";\n    }\n    static calculateMonthlyUsage(plan, propertiesPerMonth, photosPerProperty) {\n        const totalPhotosNeeded = propertiesPerMonth * photosPerProperty;\n        const averageLimit = this.getPhotoLimit(plan, Math.floor(propertiesPerMonth / 2));\n        const planCanHandle = photosPerProperty <= averageLimit;\n        return {\n            totalPhotosNeeded,\n            planCanHandle,\n            suggestedPlan: planCanHandle ? undefined : this.getRecommendedPlan(photosPerProperty)\n        };\n    }\n    // FIXED: Simple method for current system\n    static async canPerformOperation(operationType, property, user) {\n        const cost = this.getOperationCost(operationType);\n        // Simple check - just return allowed for now since we have basic validation in API\n        return {\n            allowed: true\n        };\n    }\n    // ENHANCED: Get AI agent prompt context with spatial intelligence\n    static getAgentPromptContext(operationType, roomType, designStyle) {\n        const operation = this.getOperationDetails(operationType);\n        if (!operation) return \"\";\n        // Build comprehensive spatial context\n        let context = this.buildSpatialIntelligencePrompt(operationType, roomType, designStyle);\n        return context;\n    }\n    // NEW: Build comprehensive spatial intelligence prompt\n    static buildSpatialIntelligencePrompt(operationType, roomType, designStyle) {\n        const operation = this.getOperationDetails(operationType);\n        if (!operation) return \"\";\n        let prompt = `You are an expert ${operation.agentRole} with advanced spatial intelligence and architectural awareness.\\n\\n`;\n        // Add spatial analysis instructions\n        prompt += this.getSpatialAnalysisInstructions();\n        // Add room-specific spatial context\n        if (roomType) {\n            prompt += this.getRoomSpecificSpatialContext(roomType);\n        }\n        // Add operation-specific instructions\n        prompt += this.getOperationSpecificInstructions(operationType, designStyle);\n        // Add critical spatial constraints\n        prompt += this.getSpatialConstraints();\n        return prompt;\n    }\n    // NEW: Core spatial analysis instructions\n    static getSpatialAnalysisInstructions() {\n        return `\nSPATIAL ANALYSIS PROTOCOL:\n1. ANALYZE the room's architectural features:\n   - Identify all doors, windows, and their swing directions\n   - Locate built-in elements (cabinets, counters, fixtures)\n   - Assess ceiling height and any structural elements\n   - Note electrical outlets, switches, and lighting fixtures\n   - Identify traffic flow patterns and circulation paths\n\n2. MEASURE spatial relationships:\n   - Estimate room dimensions and proportions\n   - Calculate available floor space for furniture\n   - Identify focal points and natural gathering areas\n   - Assess sight lines and visual connections\n\n3. UNDERSTAND functional zones:\n   - Primary activity areas (seating, sleeping, working)\n   - Secondary support areas (storage, circulation)\n   - Service areas (access to utilities, maintenance)\n\n`;\n    }\n    // NEW: Room-specific spatial context\n    static getRoomSpecificSpatialContext(roomType) {\n        const roomContexts = {\n            living_room: `\nLIVING ROOM SPATIAL REQUIREMENTS:\n- Maintain 3-4 feet of circulation space around seating areas\n- Position seating to face each other for conversation (8-10 feet apart maximum)\n- Keep pathways clear from main entrance to other rooms\n- Place coffee table 14-18 inches from sofa front edge\n- Ensure TV viewing distance is 1.5-2.5x the screen diagonal\n- Leave 6-8 inches between furniture and walls for cleaning\n- Create visual balance with furniture placement and scale\n\n`,\n            bedroom: `\nBEDROOM SPATIAL REQUIREMENTS:\n- Maintain 2-3 feet of clearance around bed for making and accessing\n- Position bed away from direct door alignment for privacy\n- Ensure adequate space for closet and drawer access (3+ feet)\n- Place nightstands within arm's reach of bed occupants\n- Keep pathways to bathroom/closet unobstructed\n- Consider window placement for natural light and ventilation\n- Scale furniture appropriately to room size (avoid oversized pieces)\n\n`,\n            kitchen: `\nKITCHEN SPATIAL REQUIREMENTS:\n- Maintain work triangle efficiency (sink, stove, refrigerator)\n- Ensure 42-48 inches of clearance for walkways and appliance access\n- Keep countertop work areas clear and functional\n- Position islands with adequate circulation space (36+ inches)\n- Maintain clear sight lines for safety while cooking\n- Consider cabinet door and drawer swing clearances\n- Ensure proper ventilation and lighting over work areas\n\n`,\n            dining_room: `\nDINING ROOM SPATIAL REQUIREMENTS:\n- Allow 24-30 inches per person at dining table\n- Maintain 36-42 inches between table edge and walls/furniture for chair access\n- Position table to allow natural conversation flow\n- Ensure adequate lighting over dining surface\n- Keep serving areas accessible from kitchen\n- Consider buffet/sideboard placement for functionality\n- Scale chandelier/lighting to table size (1/2 to 2/3 table width)\n\n`,\n            bathroom: `\nBATHROOM SPATIAL REQUIREMENTS:\n- Maintain required clearances around fixtures (15\" from centerline for toilets)\n- Ensure door swing doesn't interfere with fixture use\n- Keep towel storage within reach of shower/tub\n- Position mirrors for optimal lighting and use\n- Maintain clear floor space for safety (especially near wet areas)\n- Consider accessibility and ease of cleaning\n- Ensure adequate ventilation and moisture control\n\n`,\n            office: `\nOFFICE SPATIAL REQUIREMENTS:\n- Position desk to minimize glare on computer screen\n- Maintain 3+ feet behind desk chair for movement\n- Ensure adequate lighting for task work (avoid shadows)\n- Keep frequently used items within arm's reach\n- Position storage for easy access without disrupting workflow\n- Consider cable management and electrical access\n- Create distinct zones for different work activities\n\n`\n        };\n        return roomContexts[roomType.toLowerCase()] || `\nGENERAL ROOM SPATIAL REQUIREMENTS:\n- Maintain appropriate circulation and clearance spaces\n- Consider the room's primary function and user needs\n- Ensure furniture scale is appropriate to room size\n- Keep pathways clear and logical\n- Position elements for optimal functionality and safety\n\n`;\n    }\n    // NEW: Operation-specific spatial instructions\n    static getOperationSpecificInstructions(operationType, designStyle) {\n        let instructions = \"\";\n        switch(operationType){\n            case \"declutter\":\n                instructions = `\nDECLUTTER SPATIAL OBJECTIVES:\n- Remove ALL furniture, personal items, and clutter while preserving architectural integrity\n- Maintain original room proportions and spatial relationships\n- Preserve built-in elements, fixtures, and permanent installations\n- Keep structural elements (walls, columns, beams) intact\n- Maintain original flooring, ceiling, and wall finishes\n- Preserve window and door openings and their trim\n- Result should be a clean, empty space that showcases the room's bones\n\n`;\n                break;\n            case \"staging\":\n                instructions = `\nSTAGING SPATIAL OBJECTIVES:\n- Add furniture and decor that enhances the room's spatial qualities\n- Create inviting, functional arrangements that showcase the space's potential\n- Use appropriately scaled furniture that doesn't overwhelm the room\n- Establish clear circulation paths and functional zones\n- Highlight architectural features and natural light\n- Create visual interest while maintaining spatial harmony\n- Choose pieces that appeal to the broadest range of potential buyers/renters\n\nFURNITURE PLACEMENT PRINCIPLES:\n- Float furniture away from walls when space allows (creates sense of spaciousness)\n- Create conversation areas with seating facing each other\n- Use rugs to define spaces and add warmth\n- Add vertical elements (tall plants, floor lamps) to draw the eye up\n- Balance visual weight throughout the room\n- Layer lighting (ambient, task, accent) for depth and warmth\n\n`;\n                break;\n            case \"chain\":\n                instructions = `\nCHAIN OPERATION SPATIAL OBJECTIVES:\n- First phase: Complete decluttering following spatial preservation principles\n- Second phase: Strategic staging that maximizes the revealed spatial potential\n- Ensure seamless transition between phases maintains spatial integrity\n- Final result should showcase dramatic transformation while respecting architecture\n\n`;\n                break;\n        }\n        if (designStyle && operationType !== \"declutter\") {\n            instructions += this.getDesignStyleSpatialGuidance(designStyle);\n        }\n        return instructions;\n    }\n    // NEW: Design style spatial guidance\n    static getDesignStyleSpatialGuidance(designStyle) {\n        const styleGuidance = {\n            modern: `\nMODERN STYLE SPATIAL APPROACH:\n- Emphasize clean lines and geometric arrangements\n- Use minimal, well-proportioned furniture with sleek profiles\n- Create open, uncluttered spaces with strategic negative space\n- Choose furniture with legs to maintain visual lightness\n- Use neutral colors to enhance spatial perception\n- Incorporate built-in storage solutions when possible\n\n`,\n            traditional: `\nTRADITIONAL STYLE SPATIAL APPROACH:\n- Create cozy, intimate seating arrangements\n- Use substantial furniture pieces appropriate to room scale\n- Layer textiles and accessories for warmth and comfort\n- Establish formal arrangements with symmetrical balance\n- Include classic proportions and time-tested layouts\n- Add architectural details that enhance traditional character\n\n`,\n            contemporary: `\nCONTEMPORARY STYLE SPATIAL APPROACH:\n- Blend comfort with clean, updated aesthetics\n- Use furniture with interesting shapes while maintaining functionality\n- Create flexible spaces that can adapt to different uses\n- Incorporate current trends while respecting spatial principles\n- Balance bold elements with neutral backgrounds\n- Emphasize natural light and connection to outdoors\n\n`,\n            minimalist: `\nMINIMALIST STYLE SPATIAL APPROACH:\n- Use only essential furniture pieces with perfect proportions\n- Maximize negative space to create sense of calm and openness\n- Choose multi-functional pieces to reduce visual clutter\n- Emphasize quality over quantity in all selections\n- Use monochromatic or very limited color palettes\n- Let architectural features be the primary visual interest\n\n`,\n            industrial: `\nINDUSTRIAL STYLE SPATIAL APPROACH:\n- Expose and celebrate structural elements (beams, pipes, brick)\n- Use furniture with metal and wood combinations\n- Create open, loft-like arrangements with high ceilings\n- Incorporate vintage or repurposed pieces with character\n- Use raw materials and honest construction methods\n- Maintain utilitarian functionality in all design choices\n\n`\n        };\n        return styleGuidance[designStyle.toLowerCase()] || \"\";\n    }\n    // NEW: Critical spatial constraints and safety guidelines\n    static getSpatialConstraints() {\n        return `\nCRITICAL SPATIAL CONSTRAINTS - NEVER VIOLATE:\n❌ DO NOT place furniture blocking doorways or natural traffic paths\n❌ DO NOT position items where they would interfere with door/window operation\n❌ DO NOT place furniture too close to heat sources or electrical panels\n❌ DO NOT block access to built-in storage or utility areas\n❌ DO NOT create arrangements that feel cramped or claustrophobic\n❌ DO NOT use furniture that is dramatically out of scale with the room\n❌ DO NOT place heavy items where they appear unstable or unsafe\n❌ DO NOT block natural light sources unnecessarily\n❌ DO NOT create dead-end spaces or awkward circulation patterns\n❌ DO NOT ignore the room's architectural hierarchy and focal points\n\n✅ ALWAYS ensure furniture appears stable and properly supported\n✅ ALWAYS maintain logical traffic flow and circulation\n✅ ALWAYS respect the room's proportions and scale\n✅ ALWAYS consider real-world functionality and daily use\n✅ ALWAYS enhance rather than fight the existing architecture\n✅ ALWAYS create inviting, livable spaces that feel authentic\n✅ ALWAYS use appropriate lighting to enhance spatial perception\n✅ ALWAYS consider safety and accessibility in all arrangements\n\nFINAL VERIFICATION:\nBefore completing the image, mentally walk through the space and verify:\n- Can people move naturally through the room?\n- Does the furniture arrangement support the room's intended function?\n- Are all pieces appropriately scaled and positioned?\n- Does the overall composition feel balanced and harmonious?\n- Would this space be appealing and functional for real occupants?\n`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/context-engine/ContextEngine.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/useStore.ts":
/*!*******************************!*\
  !*** ./src/store/useStore.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contextLimit: () => (/* binding */ contextLimit),\n/* harmony export */   contextUsed: () => (/* binding */ contextUsed),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// Helper function to get photo limits based on plan\nfunction getPlanPhotoLimit(plan) {\n    const limits = {\n        free: 2,\n        starter: 5,\n        pro: 15,\n        team: 50\n    };\n    return limits[plan] || 2;\n}\nconst useStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // User state\n        user: null,\n        setUser: (user)=>set({\n                user\n            }),\n        clearUser: ()=>set({\n                user: null,\n                selectedProperty: null,\n                photosUsed: 0,\n                photoLimit: 0\n            }),\n        // Property state\n        selectedProperty: null,\n        setSelectedProperty: (property)=>{\n            set({\n                selectedProperty: property\n            });\n            if (property) {\n                set({\n                    photosUsed: property.photosUsed,\n                    photoLimit: property.photoLimit\n                });\n            }\n        },\n        // Photo usage state\n        photosUsed: 0,\n        photoLimit: 0,\n        updatePhotoUsage: (used)=>{\n            set({\n                photosUsed: used\n            });\n            const { selectedProperty } = get();\n            if (selectedProperty) {\n                set({\n                    selectedProperty: {\n                        ...selectedProperty,\n                        photosUsed: used\n                    }\n                });\n            }\n        },\n        setPhotoLimits: (used, limit)=>set({\n                photosUsed: used,\n                photoLimit: limit\n            }),\n        // UI state\n        sidebarOpen: false,\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        // Processing state\n        isProcessing: false,\n        setIsProcessing: (processing)=>set({\n                isProcessing: processing\n            }),\n        // Error state\n        error: null,\n        setError: (error)=>set({\n                error\n            }),\n        clearError: ()=>set({\n                error: null\n            })\n    }), {\n    name: \"staiger-store\",\n    partialize: (state)=>({\n            user: state.user,\n            selectedProperty: state.selectedProperty,\n            photosUsed: state.photosUsed,\n            photoLimit: state.photoLimit\n        })\n}));\n// Aliases for backward compatibility with existing code that uses context terminology\nconst contextUsed = (state)=>state.photosUsed;\nconst contextLimit = (state)=>state.photoLimit;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/useStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"31c1960a3620\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhaWdlci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGQ4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMxYzE5NjBhMzYyMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/loading.tsx":
/*!***************************************!*\
  !*** ./src/app/dashboard/loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\loading.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/properties/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/properties/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\staiger\src\app\dashboard\properties\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staiger AI - Virtual Staging Platform\",\n    description: \"Transform your real estate photos with AI-powered virtual staging and decluttering\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} bg-space-900 text-foreground antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\staiger\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVO2tCQUN4Qiw0RUFBQ0M7WUFBS0QsV0FBVyxDQUFDLEVBQUVSLCtKQUFlLENBQUMseUNBQXlDLENBQUM7c0JBQzNFSzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWlnZXIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJTdGFpZ2VyIEFJIC0gVmlydHVhbCBTdGFnaW5nIFBsYXRmb3JtXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlRyYW5zZm9ybSB5b3VyIHJlYWwgZXN0YXRlIHBob3RvcyB3aXRoIEFJLXBvd2VyZWQgdmlydHVhbCBzdGFnaW5nIGFuZCBkZWNsdXR0ZXJpbmdcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gYmctc3BhY2UtOTAwIHRleHQtZm9yZWdyb3VuZCBhbnRpYWxpYXNlZGB9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFpZ2VyLy4vc3JjL2FwcC9mYXZpY29uLmljbz9jZDk0Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/zustand","vendor-chunks/use-sync-external-store"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fproperties%2Fpage&page=%2Fdashboard%2Fproperties%2Fpage&appPaths=%2Fdashboard%2Fproperties%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fproperties%2Fpage.tsx&appDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CProject2Dream%5CDesktop%5Cstaiger&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();