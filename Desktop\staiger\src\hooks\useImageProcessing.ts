import { useState, useCallback } from 'react';
import { useStore } from '@/store/useStore';
import { Property } from '@/types';

interface ProcessImageOptions {
  propertyId: string;
  operation: 'declutter' | 'staging' | 'chain';
  roomType: string;
  designStyle?: string;
  image: File;
  forceReprocess?: boolean;
}

interface ProcessingResult {
  success: boolean;
  jobId: string;
  outputImageUrl: string;
  photosConsumed: number;
}

interface QueueItem {
  id: string;
  file: File;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: ProcessingResult;
  error?: string;
}

interface ProcessQueueOptions {
  propertyId: string;
  operation: 'declutter' | 'staging' | 'chain';
  roomType: string;
  designStyle?: string;
  images: File[];
  forceReprocess?: boolean;
}

export function useImageProcessing() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [currentProcessingIndex, setCurrentProcessingIndex] = useState<number>(-1);
  const { setIsProcessing: setGlobalProcessing } = useStore();

  const processImage = async ({
    propertyId,
    operation,
    roomType,
    designStyle,
    image,
    forceReprocess,
  }: ProcessImageOptions): Promise<ProcessingResult | null> => {
    try {
      setError(null);

      // Validate image file
      if (!image || !image.type.startsWith('image/')) {
        throw new Error('Please select a valid image file');
      }

      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (image.size > maxSize) {
        throw new Error('Image file size must be less than 10MB');
      }

      const formData = new FormData();
      formData.append('propertyId', propertyId);
      formData.append('operation', operation);
      formData.append('roomType', roomType);
      if (designStyle) {
        formData.append('designStyle', designStyle);
      }
      if (forceReprocess) {
        formData.append('forceReprocess', 'true');
      }
      formData.append('image', image);

      const response = await fetch('/api/process', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle duplicate detection with reprocess option
        if (response.status === 409 && data.canReprocess) {
          throw new Error(`${data.error}\n\n${data.suggestion}`);
        }
        throw new Error(data.error || 'Failed to process image');
      }

      // Handle successful processing
      if (data.success) {
        return {
          success: true,
          jobId: data.jobId,
          outputImageUrl: data.outputImageUrl,
          photosConsumed: data.photosConsumed,
        };
      } else {
        throw new Error('Image processing failed');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const processQueue = useCallback(async ({
    propertyId,
    operation,
    roomType,
    designStyle,
    images,
    forceReprocess,
  }: ProcessQueueOptions): Promise<ProcessingResult[]> => {
    try {
      setIsProcessing(true);
      setGlobalProcessing(true);
      setError(null);
      setCurrentProcessingIndex(-1);

      // Initialize queue
      const initialQueue: QueueItem[] = images.map((file, index) => ({
        id: `${Date.now()}-${index}`,
        file,
        status: 'pending',
      }));

      setQueue(initialQueue);

      const results: ProcessingResult[] = [];

      // Process each image sequentially
      for (let i = 0; i < images.length; i++) {
        const item = initialQueue[i];
        setCurrentProcessingIndex(i);

        // Update queue item status to processing
        setQueue(prev => prev.map(qItem => 
          qItem.id === item.id 
            ? { ...qItem, status: 'processing' }
            : qItem
        ));

        try {
          const result = await processImage({
            propertyId,
            operation,
            roomType,
            designStyle,
            image: item.file,
            forceReprocess,
          });

          if (result) {
            results.push(result);
            
            // Update queue item status to completed
            setQueue(prev => prev.map(qItem => 
              qItem.id === item.id 
                ? { ...qItem, status: 'completed', result }
                : qItem
            ));
          } else {
            throw new Error('Processing failed');
          }
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Processing failed';
          
          // Update queue item status to failed
          setQueue(prev => prev.map(qItem => 
            qItem.id === item.id 
              ? { ...qItem, status: 'failed', error: errorMessage }
              : qItem
          ));

          // Continue processing other images even if one fails
          console.error(`Failed to process image ${i + 1}:`, errorMessage);
        }
      }

      setCurrentProcessingIndex(-1);
      return results;

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return [];
    } finally {
      setIsProcessing(false);
      setGlobalProcessing(false);
    }
  }, []);

  const clearQueue = useCallback(() => {
    setQueue([]);
    setCurrentProcessingIndex(-1);
  }, []);

  const createProperty = async (address: string): Promise<Property | null> => {
    try {
      setError(null);

      // Validate address
      if (!address || address.trim().length < 5) {
        throw new Error('Please enter a valid property address');
      }

      const response = await fetch('/api/properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ address: address.trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create property');
      }

      return data.property;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return null;
    }
  };

  const getProperties = async (): Promise<Property[]> => {
    try {
      setError(null);

      const response = await fetch('/api/properties');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch properties');
      }

      return data.properties || [];
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return [];
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    processImage,
    processQueue,
    createProperty,
    getProperties,
    isProcessing,
    error,
    queue,
    currentProcessingIndex,
    clearQueue,
    clearError,
  };
} 