import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  email: string;
  name?: string;
  plan?: 'free' | 'starter' | 'pro' | 'team';
}

interface SelectedProperty {
  id: string;
  address: string;
  type: string;
  photosUsed: number;
  photoLimit: number;
  createdAt: string;
}

interface StoreState {
  // User state
  user: User | null;
  setUser: (user: User | null) => void;
  clearUser: () => void;
  
  // Property state
  selectedProperty: SelectedProperty | null;
  setSelectedProperty: (property: SelectedProperty | null) => void;
  
  // Photo usage state
  photosUsed: number;
  photoLimit: number;
  updatePhotoUsage: (used: number) => void;
  setPhotoLimits: (used: number, limit: number) => void;
  
  // UI state
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  
  // Processing state
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
  
  // Error state
  error: string | null;
  setError: (error: string | null) => void;
  clearError: () => void;
}

// Helper function to get photo limits based on plan
function getPlanPhotoLimit(plan: string): number {
  const limits = {
    free: 2,
    starter: 5,
    pro: 15,
    team: 50,
  };
  return limits[plan as keyof typeof limits] || 2;
}

export const useStore = create<StoreState>()(
  persist(
    (set, get) => ({
      // User state
      user: null,
      setUser: (user) => set({ user }),
      clearUser: () => set({ 
        user: null, 
        selectedProperty: null, 
        photosUsed: 0, 
        photoLimit: 0 
      }),
      
      // Property state
      selectedProperty: null,
      setSelectedProperty: (property) => {
        set({ selectedProperty: property });
        if (property) {
          set({ 
            photosUsed: property.photosUsed, 
            photoLimit: property.photoLimit 
          });
        }
      },
      
      // Photo usage state
      photosUsed: 0,
      photoLimit: 0,
      updatePhotoUsage: (used) => {
        set({ photosUsed: used });
        const { selectedProperty } = get();
        if (selectedProperty) {
          set({
            selectedProperty: {
              ...selectedProperty,
              photosUsed: used,
            }
          });
        }
      },
      setPhotoLimits: (used, limit) => set({ photosUsed: used, photoLimit: limit }),
      
      // UI state
      sidebarOpen: false,
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      
      // Processing state
      isProcessing: false,
      setIsProcessing: (processing) => set({ isProcessing: processing }),
      
      // Error state
      error: null,
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),
    }),
    {
      name: 'staiger-store',
      partialize: (state) => ({
        user: state.user,
        selectedProperty: state.selectedProperty,
        photosUsed: state.photosUsed,
        photoLimit: state.photoLimit,
      }),
    }
  )
);

// Aliases for backward compatibility with existing code that uses context terminology
export const contextUsed = (state: StoreState) => state.photosUsed;
export const contextLimit = (state: StoreState) => state.photoLimit; 