"use client";

import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Check, Crown, Star, Zap, ArrowRight, Users, Shield, Headphones } from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  description: string;
  features: string[];
  photosPerProperty: number;
  maxProperties: number;
  isPopular?: boolean;
  isEnterprise?: boolean;
}

export default function PricingPage() {
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>('month');

  const plans: PricingPlan[] = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      interval: billingInterval,
      description: 'Perfect for trying out our AI virtual staging',
      photosPerProperty: 2,
      maxProperties: 5,
      features: [
        '2 photos per property',
        'Up to 5 properties',
        'Basic virtual staging',
        'Standard processing speed',
        'Email support',
        'Watermarked images',
      ],
    },
    {
      id: 'starter',
      name: 'Starter',
      price: billingInterval === 'month' ? 29 : 290,
      interval: billingInterval,
      description: 'Great for individual agents and small teams',
      photosPerProperty: 5,
      maxProperties: 25,
      features: [
        '5 photos per property',
        'Up to 25 properties',
        'Advanced virtual staging',
        'Priority processing',
        'Email & chat support',
        'Watermark-free images',
        'HD image quality',
        'Multiple design styles',
      ],
    },
    {
      id: 'pro',
      name: 'Pro',
      price: billingInterval === 'month' ? 79 : 790,
      interval: billingInterval,
      description: 'Perfect for growing real estate businesses',
      photosPerProperty: 15,
      maxProperties: 100,
      isPopular: true,
      features: [
        '15 photos per property',
        'Up to 100 properties',
        'Premium virtual staging',
        'Fastest processing',
        'Priority support',
        'Watermark-free images',
        '4K image quality',
        'All design styles',
        'Batch processing',
        'API access',
        'Team collaboration',
      ],
    },
    {
      id: 'team',
      name: 'Team',
      price: billingInterval === 'month' ? 199 : 1990,
      interval: billingInterval,
      description: 'For large teams and real estate agencies',
      photosPerProperty: 50,
      maxProperties: 500,
      features: [
        '50 photos per property',
        'Up to 500 properties',
        'Enterprise virtual staging',
        'Instant processing',
        'Dedicated support',
        'Watermark-free images',
        '4K+ image quality',
        'Custom design styles',
        'Advanced batch processing',
        'Full API access',
        'Team management',
        'Custom integrations',
        'White-label options',
      ],
    },
  ];

  const enterpriseFeatures = [
    'Unlimited photos per property',
    'Unlimited properties',
    'Custom AI model training',
    'On-premise deployment',
    'SLA guarantee',
    'Dedicated account manager',
    'Custom integrations',
    'White-label solution',
    'Advanced analytics',
    'Priority feature requests',
  ];

  const handlePlanSelect = (planId: string) => {
    if (planId === 'free') {
      window.location.href = '/login';
    } else {
      // In a real app, this would redirect to payment processing
      window.location.href = `/login?plan=${planId}&billing=${billingInterval}`;
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <Star className="h-6 w-6" />;
      case 'starter':
        return <Zap className="h-6 w-6" />;
      case 'pro':
        return <Crown className="h-6 w-6" />;
      case 'team':
        return <Users className="h-6 w-6" />;
      default:
        return <Star className="h-6 w-6" />;
    }
  };

  const getPlanColor = (planId: string) => {
    switch (planId) {
      case 'free':
        return 'text-gray-400';
      case 'starter':
        return 'text-blue-400';
      case 'pro':
        return 'text-purple-400';
      case 'team':
        return 'text-gold-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-space-900">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-up">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-space-400 max-w-2xl mx-auto">
            Transform your real estate photos with AI-powered virtual staging. 
            Start free, upgrade as you grow.
          </p>
        </div>

        {/* Billing Toggle */}
        <div className="flex justify-center mb-12 animate-fade-up" style={{ animationDelay: '0.1s' }}>
          <div className="bg-space-800/50 p-1 rounded-lg border border-space-700">
            <button
              onClick={() => setBillingInterval('month')}
              className={`px-6 py-2 rounded-md transition-all duration-200 ${
                billingInterval === 'month'
                  ? 'bg-royal-500 text-white shadow-lg'
                  : 'text-space-400 hover:text-foreground'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingInterval('year')}
              className={`px-6 py-2 rounded-md transition-all duration-200 ${
                billingInterval === 'year'
                  ? 'bg-royal-500 text-white shadow-lg'
                  : 'text-space-400 hover:text-foreground'
              }`}
            >
              Yearly
              <span className="ml-2 text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">
                Save 17%
              </span>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {plans.map((plan, index) => (
            <Card
              key={plan.id}
              className={`relative overflow-hidden transition-all duration-300 hover:scale-105 ${
                plan.isPopular
                  ? 'ring-2 ring-royal-500 shadow-2xl shadow-royal-500/20'
                  : ''
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {plan.isPopular && (
                <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-royal-500 to-purple-600 text-white text-center py-2 text-sm font-medium">
                  Most Popular
                </div>
              )}
              
              <CardHeader className={plan.isPopular ? 'pt-12' : ''}>
                <div className="flex items-center space-x-3 mb-4">
                  <div className={getPlanColor(plan.id)}>
                    {getPlanIcon(plan.id)}
                  </div>
                  <h3 className="text-xl font-bold text-foreground">{plan.name}</h3>
                </div>
                
                <div className="mb-4">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-foreground">
                      ${plan.price}
                    </span>
                    {plan.price > 0 && (
                      <span className="text-space-400 ml-2">
                        /{billingInterval}
                      </span>
                    )}
                  </div>
                  {billingInterval === 'year' && plan.price > 0 && (
                    <p className="text-sm text-green-400 mt-1">
                      ${Math.round(plan.price / 12)}/month billed annually
                    </p>
                  )}
                </div>
                
                <p className="text-space-400 text-sm">{plan.description}</p>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Key Stats */}
                <div className="bg-space-800/30 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-space-400">Photos per property</span>
                    <span className="font-bold text-royal-400">{plan.photosPerProperty}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-space-400">Max properties</span>
                    <span className="font-bold text-royal-400">{plan.maxProperties}</span>
                  </div>
                </div>
                
                {/* Features */}
                <div className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start space-x-3">
                      <Check className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-space-300">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <Button
                  onClick={() => handlePlanSelect(plan.id)}
                  variant={plan.isPopular ? 'premium' : 'outline'}
                  className="w-full"
                  size="lg"
                >
                  {plan.price === 0 ? 'Get Started Free' : `Choose ${plan.name}`}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Enterprise Section */}
        <Card className="mb-16 animate-fade-up" style={{ animationDelay: '0.5s' }}>
          <CardContent className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <div className="flex items-center space-x-3 mb-4">
                  <Shield className="h-8 w-8 text-gold-400" />
                  <h3 className="text-2xl font-bold text-foreground">Enterprise</h3>
                </div>
                <p className="text-space-400 mb-6">
                  Custom solutions for large organizations with specific needs. 
                  Get dedicated support, custom integrations, and enterprise-grade security.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {enterpriseFeatures.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <Check className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-space-300">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div className="text-center">
                <div className="mb-6">
                  <div className="text-4xl font-bold text-foreground mb-2">Custom</div>
                  <p className="text-space-400">Tailored to your needs</p>
                </div>
                <Button
                  variant="premium"
                  size="lg"
                  className="mb-4"
                  onClick={() => window.location.href = '/contact'}
                >
                  Contact Sales
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
                <div className="flex items-center justify-center space-x-2 text-sm text-space-400">
                  <Headphones className="h-4 w-4" />
                  <span>Dedicated account manager included</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* FAQ Section */}
        <div className="text-center mb-16 animate-fade-up" style={{ animationDelay: '0.6s' }}>
          <h2 className="text-3xl font-bold text-foreground mb-8">Frequently Asked Questions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card>
              <CardContent className="p-6">
                <h4 className="font-semibold text-foreground mb-3">
                  What counts as a "photo"?
                </h4>
                <p className="text-space-400 text-sm">
                  Each AI operation (decluttering, staging, or enhancement) counts as one photo. 
                  The "chain" operation (declutter + stage) counts as 2 photos.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h4 className="font-semibold text-foreground mb-3">
                  Can I upgrade or downgrade anytime?
                </h4>
                <p className="text-space-400 text-sm">
                  Yes! You can change your plan at any time. Upgrades take effect immediately, 
                  and downgrades take effect at the next billing cycle.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h4 className="font-semibold text-foreground mb-3">
                  Do unused photos roll over?
                </h4>
                <p className="text-space-400 text-sm">
                  Photo limits reset each month. Unused photos don't roll over, 
                  but you can process more photos by upgrading your plan.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h4 className="font-semibold text-foreground mb-3">
                  Is there a free trial?
                </h4>
                <p className="text-space-400 text-sm">
                  Yes! Our Free plan lets you try the platform with 2 photos per property. 
                  No credit card required to get started.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center animate-fade-up" style={{ animationDelay: '0.7s' }}>
          <Card className="bg-gradient-to-r from-royal-500/10 to-purple-600/10 border-royal-500/20">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Ready to transform your real estate photos?
              </h3>
              <p className="text-space-400 mb-6 max-w-2xl mx-auto">
                Join thousands of real estate professionals who are already using Staiger AI 
                to create stunning virtual staging in seconds.
              </p>
              <Button
                variant="premium"
                size="lg"
                onClick={() => window.location.href = '/login'}
              >
                Start Free Today
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 