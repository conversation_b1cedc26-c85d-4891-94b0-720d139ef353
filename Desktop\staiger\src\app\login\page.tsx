"use client";

import { AuthForm } from '@/components/auth/AuthForm';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-royal-900/20 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center animate-fade-up">
          <Link 
            href="/" 
            className="inline-flex items-center text-sm text-space-400 hover:text-royal-400 transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to home
          </Link>
        </div>
        
        <div className="animate-fade-up" style={{ animationDelay: '0.2s' }}>
          <AuthForm />
        </div>
        
        <div className="text-center text-xs text-space-500 animate-fade-up" style={{ animationDelay: '0.4s' }}>
          <p>
            By signing in, you agree to our{' '}
            <Link href="/terms" className="text-royal-400 hover:underline">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" className="text-royal-400 hover:underline">
              Privacy Policy
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
} 