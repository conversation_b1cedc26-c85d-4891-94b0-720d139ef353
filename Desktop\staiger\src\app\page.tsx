"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, Spa<PERSON><PERSON>, Zap, Shield, Star } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card';

export default function HomePage() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-royal-900/20">
      {/* Hero Section */}
      <section className="relative overflow-hidden px-6 py-24 sm:py-32 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
              Transform Properties with{' '}
              <span className="bg-gradient-to-r from-royal-400 to-purple-400 bg-clip-text text-transparent">
                AI Magic
              </span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-space-300">
              Professional virtual staging and decluttering for real estate. 
              Turn empty rooms into stunning showcase spaces in seconds.
            </p>
          </div>
          
          <div className={`mt-10 flex items-center justify-center gap-x-6 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <Link href="/login">
              <Button variant="premium" size="lg">
                Start Free Trial
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Button variant="outline" size="lg">
              View Gallery
            </Button>
          </div>

          {/* Stats */}
          <div className={`mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3 transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="text-center">
              <div className="text-3xl font-bold text-royal-400">10k+</div>
              <div className="text-sm text-space-400">Properties Staged</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royal-400">99%</div>
              <div className="text-sm text-space-400">Client Satisfaction</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-royal-400">24/7</div>
              <div className="text-sm text-space-400">AI Processing</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Everything you need to stage properties
            </h2>
            <p className="mt-4 text-lg text-space-300">
              Our AI-powered platform provides professional-grade virtual staging tools
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-5xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              <Card className="animate-fade-up" style={{ animationDelay: '0.1s' }}>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="rounded-lg bg-royal-500/10 p-2">
                      <Sparkles className="h-6 w-6 text-royal-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">AI Virtual Staging</h3>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-space-300">
                    Add beautiful, realistic furniture and decor to empty rooms with our advanced AI technology.
                  </p>
                </CardContent>
              </Card>

              <Card className="animate-fade-up" style={{ animationDelay: '0.2s' }}>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="rounded-lg bg-royal-500/10 p-2">
                      <Zap className="h-6 w-6 text-royal-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Smart Decluttering</h3>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-space-300">
                    Remove clutter and unwanted items while preserving the natural structure of the room.
                  </p>
                </CardContent>
              </Card>

              <Card className="animate-fade-up" style={{ animationDelay: '0.3s' }}>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="rounded-lg bg-royal-500/10 p-2">
                      <Shield className="h-6 w-6 text-royal-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Professional Quality</h3>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-space-300">
                    High-resolution outputs with professional watermarking for your real estate business.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Simple, transparent pricing
            </h2>
            <p className="mt-4 text-lg text-space-300">
              Pay for what you use with our context-based pricing system
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-4">
            {[
              {
                name: 'Free',
                price: '$0',
                photos: '2',
                features: ['2 photos per property', 'Unlimited properties', 'Basic support', 'Standard processing'],
                delay: '0.1s'
              },
              {
                name: 'Starter',
                price: '$10',
                photos: '5',
                features: ['5 photos per property', 'Decays to 2 minimum', 'Email support', 'Priority processing'],
                delay: '0.2s'
              },
              {
                name: 'Pro',
                price: '$30',
                photos: '15',
                features: ['15 photos per property', 'Decays to 5 minimum', 'Priority support', 'Advanced styling options'],
                popular: true,
                delay: '0.3s'
              },
              {
                name: 'Team',
                price: '$99',
                photos: '50',
                features: ['50 photos per property', 'Decays to 10 minimum', '24/7 support', 'API access'],
                delay: '0.4s'
              },
            ].map((plan) => (
              <Card 
                key={plan.name} 
                variant={plan.popular ? 'premium' : 'default'}
                className="animate-fade-up relative"
                style={{ animationDelay: plan.delay }}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                    <div className="flex items-center space-x-1 rounded-full bg-gradient-to-r from-royal-500 to-purple-600 px-3 py-1 text-xs font-medium text-white">
                      <Star className="h-3 w-3" />
                      <span>Most Popular</span>
                    </div>
                  </div>
                )}
                <CardHeader>
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-white">{plan.name}</h3>
                    <div className="mt-2">
                      <span className="text-3xl font-bold text-white">{plan.price}</span>
                      <span className="text-space-400">/month</span>
                    </div>
                    <div className="mt-2 text-sm text-royal-400">
                      {plan.photos} photos per property
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm text-space-300">
                        <div className="mr-2 h-1.5 w-1.5 rounded-full bg-royal-400" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="mt-6">
                    <Button 
                      variant={plan.popular ? 'premium' : 'outline'} 
                      className="w-full"
                    >
                      Get Started
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Ready to transform your properties?
          </h2>
          <p className="mt-4 text-lg text-space-300">
            Join thousands of real estate professionals using Staiger AI
          </p>
          <div className="mt-8">
            <Link href="/login">
              <Button variant="premium" size="lg">
                Start Your Free Trial
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
