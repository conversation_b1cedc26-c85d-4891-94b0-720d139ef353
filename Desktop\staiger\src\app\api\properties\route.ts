import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { AbusePrevention } from '@/services/abuse-prevention/AbusePrevention';
import { ContextEngine } from '@/services/context-engine/ContextEngine';

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const json = await request.json();
    const { address } = json;

    // Get user session
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify address
    const isValidAddress = await AbusePrevention.verifyAddress(address);
    if (!isValidAddress) {
      return NextResponse.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Get user's properties for rate limiting
    const { data: properties } = await supabase
      .from('properties')
      .select('*')
      .eq('user_id', session.user.id);

    // Check rate limit
    const rateLimit = await AbusePrevention.checkRateLimit(
      { id: session.user.id } as any,
      properties || []
    );

    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          error: `Rate limit exceeded. Try again in ${rateLimit.cooldownMinutes} minutes.`,
        },
        { status: 429 }
      );
    }

    // Get user's plan tier
    const { data: user } = await supabase
      .from('users')
      .select('plan_tier, properties_processed')
      .eq('id', session.user.id)
      .single();

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Calculate context limit based on plan tier and properties processed
    const contextLimit = ContextEngine.getContextLimit(
      user.plan_tier as any,
      user.properties_processed
    );

    // Create new property
    const propertyId = uuidv4();
    const { data: property, error } = await supabase.from('properties').insert({
      id: propertyId,
      address,
      user_id: session.user.id,
      context_limit: contextLimit,
      context_used: 0,
      image_hashes: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Update user's properties processed count
    await supabase
      .from('users')
      .update({
        properties_processed: user.properties_processed + 1,
        updated_at: new Date().toISOString(),
      })
      .eq('id', session.user.id);

    return NextResponse.json({ property });
  } catch (error) {
    console.error('Error creating property:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get user session
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's properties
    const { data: properties, error } = await supabase
      .from('properties')
      .select('*')
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ properties });
  } catch (error) {
    console.error('Error fetching properties:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 