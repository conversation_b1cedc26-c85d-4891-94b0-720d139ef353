import { logUsage } from '@/lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { ContextEngine } from '@/services/context-engine/ContextEngine';

interface ProcessingOptions {
  operationType: 'declutter' | 'staging' | 'chain';
  roomType?: string;
  designStyle?: string;
  customPrompt?: string;
  forceReprocess?: boolean;
}

interface ProcessingResult {
  success: boolean;
  jobId?: string;
  outputImageUrl?: string;
  error?: string;
  photosConsumed: number;
}

export class AIService {
  // SECURITY: API key is stored server-side only and never exposed to frontend
  private static readonly GEMINI_API_KEY = process.env.GEMINI_API_KEY;
  private static readonly STAIGER_WATERMARK = "Staiger AI Virtualization";

  /**
   * SECURE BACKEND-ONLY IMAGE PROCESSING
   * 
   * This method runs exclusively on the server and:
   * 1. Never exposes API keys to the frontend
   * 2. Handles all AI operations server-side
   * 3. Stores processed images in our database
   * 4. Returns only the final result URLs to users
   */
  static async processImage(
    userId: string,
    propertyId: string,
    imageFile: File,
    options: ProcessingOptions,
    supabase: SupabaseClient
  ): Promise<ProcessingResult> {
    try {
      console.log('=== Starting AI Image Processing ===');
      console.log('User ID:', userId);
      console.log('Property ID:', propertyId);
      console.log('Operation:', options.operationType);
      console.log('Room Type:', options.roomType);
      console.log('Design Style:', options.designStyle);
      
      // Validate API key is available
      if (!this.GEMINI_API_KEY) {
        console.error('Gemini API key not configured');
        throw new Error('Gemini API key not configured');
      }
      console.log('API key is configured');

      // Duplicate detection is now handled by AbusePrevention service in API route

      // 2. Determine photo cost
      const photoCost = this.getPhotoCost(options.operationType);
      console.log('Photo cost:', photoCost);

      // 3. Upload original image to our secure storage
      console.log('Uploading original image...');
      const imageUrl = await this.uploadImageToStorage(imageFile, userId, propertyId, supabase);
      if (!imageUrl) {
        console.error('Failed to upload image to storage');
        return { success: false, error: 'Failed to upload image', photosConsumed: 0 };
      }
      console.log('Image uploaded successfully:', imageUrl);

      // 4. Create processing job in database for tracking (using server-side client)
      console.log('Creating processing job...');
      const jobId = crypto.randomUUID();
      const { data: job, error: jobError } = await supabase
        .from('processing_jobs')
        .insert({
          id: jobId,
          user_id: userId,
          property_id: propertyId,
          operation_type: options.operationType,
          photo_cost: photoCost,
          input_image_url: imageUrl,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (jobError) {
        console.error('Error creating processing job:', jobError);
        return { success: false, error: 'Failed to create processing job', photosConsumed: 0 };
      }
      console.log('Processing job created:', job.id);

      // 5. Store image fingerprint for future duplicate detection
      // TEMPORARILY DISABLED until job_id column is added to database
      // await this.storeImageFingerprint(userId, propertyId, imageFile, job.id, supabase);

      // 6. Update job status to processing
      console.log('Updating job status to processing...');
      await supabase
        .from('processing_jobs')
        .update({ 
          status: 'processing',
          updated_at: new Date().toISOString()
        })
        .eq('id', job.id);

      // 7. Process image with AI (ALL HAPPENS SERVER-SIDE)
      console.log('Starting AI processing...');
      let outputImageUrl: string | null = null;

      switch (options.operationType) {
        case 'declutter':
          console.log('Processing with declutter agent...');
          outputImageUrl = await this.performDeclutterOperation(imageUrl, options, supabase);
          break;
        case 'staging':
          console.log('Processing with staging agent...');
          outputImageUrl = await this.performStagingOperation(imageUrl, options, supabase);
          break;
        case 'chain':
          console.log('Processing with chain agent...');
          // First declutter, then stage
          const declutteredUrl = await this.performDeclutterOperation(imageUrl, options, supabase);
          if (declutteredUrl) {
            outputImageUrl = await this.performStagingOperation(declutteredUrl, options, supabase);
          }
          break;
      }

      console.log('AI processing completed. Output URL:', outputImageUrl);

      if (outputImageUrl) {
        // 8. Add watermark to processed image
        console.log('Adding watermark...');
        const watermarkedUrl = await this.addWatermarkToImage(outputImageUrl);
        
        // 9. Update job as completed
        console.log('Updating job status to completed...');
        await supabase
          .from('processing_jobs')
          .update({
            status: 'completed',
            output_image_url: watermarkedUrl || outputImageUrl,
            updated_at: new Date().toISOString(),
          })
          .eq('id', job.id);
        
        // 10. Log usage for billing/analytics
        console.log('Logging usage...');
        await logUsage(userId, propertyId, job.id, photoCost, options.operationType);

        console.log('=== AI Processing Completed Successfully ===');
        return {
          success: true,
          jobId: job.id,
          outputImageUrl: watermarkedUrl || outputImageUrl,
          photosConsumed: photoCost
        };
      } else {
        console.error('AI processing failed - no output image URL');
        // 11. Mark job as failed
        await supabase
          .from('processing_jobs')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString(),
          })
          .eq('id', job.id);
        return { success: false, error: 'AI processing failed', photosConsumed: 0 };
      }

    } catch (error) {
      console.error('=== AI Processing Error ===');
      console.error('Error details:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        photosConsumed: 0
      };
    }
  }

  private static getPhotoCost(operationType: string): number {
    const costs = {
      declutter: 1,
      staging: 1,
      chain: 2, // declutter + staging
    };
    return costs[operationType as keyof typeof costs] || 1;
  }

  private static async uploadImageToStorage(
    file: File,
    userId: string,
    propertyId: string,
    supabase: SupabaseClient
  ): Promise<string | null> {
    try {
      // Create unique filename with timestamp
      const fileExt = file.name.split('.').pop() || 'jpg';
      const fileName = `${userId}/${propertyId}/input/${Date.now()}.${fileExt}`;
      
      // Upload to secure storage bucket
      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Upload error:', error);
        return null;
      }

      // Get public URL for internal processing
      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (error) {
      console.error('Image upload error:', error);
      return null;
    }
  }

  private static async performDeclutterOperation(
    imageUrl: string,
    options: ProcessingOptions,
    supabase: SupabaseClient
  ): Promise<string | null> {
    try {
      console.log('Starting declutter operation for:', imageUrl);
      
      // Build declutter prompt
      const prompt = this.buildDeclutterPrompt(options);
      console.log('Declutter prompt:', prompt);
      
      // Convert image to base64 for API call
      const base64Image = await this.convertImageToBase64(imageUrl);
      console.log('Image converted to base64, length:', base64Image.length);
      
      // Call Gemini 2.0 Flash Preview Image Generation API
      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              },
              {
                inline_data: {
                  mime_type: "image/jpeg",
                  data: base64Image
                }
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.4,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
          responseModalities: ["TEXT", "IMAGE"]
        }
      };

      console.log('Making Gemini API call...');
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=${this.GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Gemini API response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gemini API error response:', errorText);
        return null;
      }

      const result = await response.json();
      console.log('Gemini API result structure:', JSON.stringify(result, null, 2));
      
      // Extract generated image from Gemini response
      if (result.candidates && result.candidates.length > 0) {
        const candidate = result.candidates[0];
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.data) {
              console.log('Found generated image in Gemini response');
              
              // Extract userId and propertyId from the original URL
              const urlParts = imageUrl.split('/');
              const storageIndex = urlParts.findIndex(part => part === 'property-images');
              
              if (storageIndex === -1 || storageIndex + 3 >= urlParts.length) {
                throw new Error('Cannot extract user/property info from URL');
              }
              
              const userId = urlParts[storageIndex + 1];
              const propertyId = urlParts[storageIndex + 2];
              
              // Upload the generated image
              return await this.uploadGeneratedImage(
                part.inlineData.data,
                'decluttered',
                userId,
                propertyId,
                supabase
              );
            }
          }
        }
      }
      
      // Fallback: copy original image if no generated image found
      console.log('No generated image found in response, using fallback');
      return await this.copyImageAsProcessed(imageUrl, 'decluttered', supabase);

    } catch (error) {
      console.error('Declutter operation error:', error);
      return null;
    }
  }

  private static async performStagingOperation(
    imageUrl: string,
    options: ProcessingOptions,
    supabase: SupabaseClient
  ): Promise<string | null> {
    try {
      console.log('Starting staging operation for:', imageUrl);
      
      // Build staging prompt
      const prompt = this.buildStagingPrompt(options);
      console.log('Staging prompt:', prompt);
      
      // Convert image to base64 for API call
      const base64Image = await this.convertImageToBase64(imageUrl);
      console.log('Image converted to base64, length:', base64Image.length);
      
      // Call Gemini 2.0 Flash Preview Image Generation API
      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              },
              {
                inline_data: {
                  mime_type: "image/jpeg",
                  data: base64Image
                }
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.6,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
          responseModalities: ["TEXT", "IMAGE"]
        }
      };

      console.log('Making Gemini API call...');
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=${this.GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Gemini API response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gemini API error response:', errorText);
        return null;
      }

      const result = await response.json();
      console.log('Gemini API result structure:', JSON.stringify(result, null, 2));
      
      // Extract generated image from Gemini response
      if (result.candidates && result.candidates.length > 0) {
        const candidate = result.candidates[0];
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.data) {
              console.log('Found generated image in Gemini response');
              
              // Extract userId and propertyId from the original URL
              const urlParts = imageUrl.split('/');
              const storageIndex = urlParts.findIndex(part => part === 'property-images');
              
              if (storageIndex === -1 || storageIndex + 3 >= urlParts.length) {
                throw new Error('Cannot extract user/property info from URL');
              }
              
              const userId = urlParts[storageIndex + 1];
              const propertyId = urlParts[storageIndex + 2];
              
              // Upload the generated image
              return await this.uploadGeneratedImage(
                part.inlineData.data,
                'staged',
                userId,
                propertyId,
                supabase
              );
            }
          }
        }
      }
      
      // Fallback: copy original image if no generated image found
      console.log('No generated image found in response, using fallback');
      return await this.copyImageAsProcessed(imageUrl, 'staged', supabase);

    } catch (error) {
      console.error('Staging operation error:', error);
      return null;
    }
  }

  private static async convertImageToBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl);
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      // Convert buffer to base64 string
      return buffer.toString('base64');
    } catch (error) {
      console.error('Error converting image to base64:', error);
      throw error;
    }
  }

  private static async uploadGeneratedImage(
    base64Data: string, 
    operation: string, 
    userId: string,
    propertyId: string,
    supabase: SupabaseClient
  ): Promise<string> {
    try {
      // Convert base64 to buffer (server-side)
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Create processed filename following user folder structure
      const timestamp = Date.now();
      const fileName = `${userId}/${propertyId}/processed/${operation}/${timestamp}.jpg`;
      
      // Upload processed image to Supabase storage
      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, buffer, {
          cacheControl: '3600',
          upsert: false,
          contentType: 'image/jpeg'
        });

      if (error) {
        console.error('Upload generated image error:', error);
        throw error;
      }

      // Get public URL for the processed image
      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading generated image:', error);
      throw error;
    }
  }

  private static buildDeclutterPrompt(options: ProcessingOptions): string {
    // Get enhanced spatial intelligence context from ContextEngine
    const spatialContext = ContextEngine.getAgentPromptContext(
      'declutter',
      options.roomType,
      options.designStyle
    );

    let prompt = spatialContext;

    if (options.customPrompt) {
      prompt += `\nADDITIONAL CUSTOM REQUIREMENTS: ${options.customPrompt}\n`;
    }

    prompt += `
FINAL DECLUTTER EXECUTION:
Analyze the provided image and remove all furniture, personal belongings, clutter, and moveable objects while preserving every architectural detail. The result must be a pristine, empty space that showcases the room's structural beauty and spatial potential.

Generate a photorealistic image that maintains the exact same perspective, lighting conditions, and architectural elements as the original, but with all removeable items eliminated.`;

    return prompt;
  }

  private static buildStagingPrompt(options: ProcessingOptions): string {
    // Get enhanced spatial intelligence context from ContextEngine
    const spatialContext = ContextEngine.getAgentPromptContext(
      'staging',
      options.roomType,
      options.designStyle
    );

    let prompt = spatialContext;

    if (options.customPrompt) {
      prompt += `\nADDITIONAL CUSTOM REQUIREMENTS: ${options.customPrompt}\n`;
    }

    prompt += `
FINAL STAGING EXECUTION:
Analyze the provided room image and add beautiful, professionally selected furniture and decor that transforms this space into an aspirational yet achievable living environment. Every piece must be:

1. SPATIALLY APPROPRIATE: Correctly scaled and positioned according to the spatial analysis above
2. FUNCTIONALLY LOGICAL: Supporting real-world use and daily activities
3. AESTHETICALLY COHESIVE: Creating visual harmony and appeal
4. ARCHITECTURALLY RESPECTFUL: Enhancing rather than competing with existing features

Generate a photorealistic image that maintains the exact same perspective and lighting as the original, but with expertly staged furniture and decor that makes potential buyers or renters fall in love with the space.

The final result should feel like a professionally photographed model home or luxury rental property.`;

    return prompt;
  }

  private static async addWatermarkToImage(imageUrl: string): Promise<string | null> {
    try {
      // TODO: Implement actual watermarking using Canvas API or image processing library
      // For now, return the original URL
      // In production, you would add "Staiger AI Virtualization" watermark in bottom-left corner
      
      return imageUrl;
    } catch (error) {
      console.error('Watermark error:', error);
      return imageUrl; // Return original if watermarking fails
    }
  }

  /**
   * Rate limiting to prevent abuse
   */
  static async checkRateLimit(userId: string): Promise<boolean> {
    try {
      // TODO: Implement proper rate limiting using Redis or database
      // For now, return true to allow all requests
      // In production, you would check against Redis or database
      // to enforce limits like 10 requests per minute per user
      console.log(`Rate limit check for user: ${userId}`);
      
      return true;
    } catch (error) {
      console.error('Rate limit check error:', error);
      return false;
    }
  }

  /**
   * Get API usage statistics for monitoring costs
   */
  static async getAPIUsageStats(): Promise<{
    totalRequests: number;
    totalCost: number;
    averageProcessingTime: number;
  }> {
    try {
      // TODO: Implement usage tracking
      // This would track API usage across all users
      // Useful for monitoring costs and performance
      
      return {
        totalRequests: 0,
        totalCost: 0,
        averageProcessingTime: 0,
      };
    } catch (error) {
      console.error('Usage stats error:', error);
      return {
        totalRequests: 0,
        totalCost: 0,
        averageProcessingTime: 0,
      };
    }
  }

  // Duplicate detection methods removed - now handled by AbusePrevention service

  // Helper method to copy original image as processed result (for testing)
  private static async copyImageAsProcessed(
    originalUrl: string,
    operation: string,
    supabase: SupabaseClient
  ): Promise<string> {
    try {
      console.log('Copying original image as processed result...');
      
      // Extract userId and propertyId from the original URL path
      // Original URL format: .../userId/propertyId/input/timestamp.ext
      const urlParts = originalUrl.split('/');
      const storageIndex = urlParts.findIndex(part => part === 'property-images');
      
      if (storageIndex === -1 || storageIndex + 3 >= urlParts.length) {
        throw new Error('Cannot extract user/property info from URL');
      }
      
      const userId = urlParts[storageIndex + 1];
      const propertyId = urlParts[storageIndex + 2];
      
      // Fetch the original image
      const response = await fetch(originalUrl);
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      // Create processed filename following user folder structure
      const timestamp = Date.now();
      const fileName = `${userId}/${propertyId}/processed/${operation}/${timestamp}.jpg`;
      
      // Upload as processed image
      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, buffer, {
          cacheControl: '3600',
          upsert: false,
          contentType: 'image/jpeg'
        });

      if (error) {
        console.error('Upload processed image error:', error);
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(data.path);

      console.log('Processed image uploaded successfully:', publicUrl);
      return publicUrl;
    } catch (error) {
      console.error('Error copying image as processed:', error);
      throw error;
    }
  }
} 