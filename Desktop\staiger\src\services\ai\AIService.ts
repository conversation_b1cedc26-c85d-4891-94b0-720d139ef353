import { logUsage } from '@/lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { ContextEngine } from '@/services/context-engine/ContextEngine';

interface ProcessingOptions {
  operationType: 'declutter' | 'staging' | 'chain';
  roomType?: string;
  designStyle?: string;
  customPrompt?: string;
  forceReprocess?: boolean;
}

interface ProcessingResult {
  success: boolean;
  jobId?: string;
  outputImageUrl?: string;
  error?: string;
  photosConsumed: number;
}

export class AIService {
  // SECURITY: API key is stored server-side only and never exposed to frontend
  private static readonly GEMINI_API_KEY = process.env.GEMINI_API_KEY;
  private static readonly STAIGER_WATERMARK = "Staiger AI Virtualization";

  /**
   * SECURE BACKEND-ONLY IMAGE PROCESSING
   * 
   * This method runs exclusively on the server and:
   * 1. Never exposes API keys to the frontend
   * 2. Handles all AI operations server-side
   * 3. Stores processed images in our database
   * 4. Returns only the final result URLs to users
   */
  static async processImage(
    userId: string,
    propertyId: string,
    imageFile: File,
    options: ProcessingOptions,
    supabase: SupabaseClient
  ): Promise<ProcessingResult> {
    try {
      console.log('=== Starting AI Image Processing ===');
      console.log('User ID:', userId);
      console.log('Property ID:', propertyId);
      console.log('Operation:', options.operationType);
      console.log('Room Type:', options.roomType);
      console.log('Design Style:', options.designStyle);
      
      // Validate API key is available
      if (!this.GEMINI_API_KEY) {
        console.error('Gemini API key not configured');
        throw new Error('Gemini API key not configured');
      }
      console.log('API key is configured');

      // 1. Simple duplicate detection (file size + name) - skip if force reprocess
      // TEMPORARILY DISABLED until job_id column is added to database
      /*
      if (!options.forceReprocess) {
        const isDuplicate = await this.checkSimpleDuplicate(userId, propertyId, imageFile, supabase);
        if (isDuplicate) {
          return { 
            success: false, 
            error: 'This image has already been virtualized. You can reprocess it for additional context cost if needed.', 
            photosConsumed: 0 
          };
        }
      }
      */

      // 2. Determine photo cost
      const photoCost = this.getPhotoCost(options.operationType);
      console.log('Photo cost:', photoCost);

      // 3. Upload original image to our secure storage
      console.log('Uploading original image...');
      const imageUrl = await this.uploadImageToStorage(imageFile, userId, propertyId, supabase);
      if (!imageUrl) {
        console.error('Failed to upload image to storage');
        return { success: false, error: 'Failed to upload image', photosConsumed: 0 };
      }
      console.log('Image uploaded successfully:', imageUrl);

      // 4. Create processing job in database for tracking (using server-side client)
      console.log('Creating processing job...');
      const jobId = crypto.randomUUID();
      const { data: job, error: jobError } = await supabase
        .from('processing_jobs')
        .insert({
          id: jobId,
          user_id: userId,
          property_id: propertyId,
          operation_type: options.operationType,
          photo_cost: photoCost,
          input_image_url: imageUrl,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (jobError) {
        console.error('Error creating processing job:', jobError);
        return { success: false, error: 'Failed to create processing job', photosConsumed: 0 };
      }
      console.log('Processing job created:', job.id);

      // 5. Store image fingerprint for future duplicate detection
      // TEMPORARILY DISABLED until job_id column is added to database
      // await this.storeImageFingerprint(userId, propertyId, imageFile, job.id, supabase);

      // 6. Update job status to processing
      console.log('Updating job status to processing...');
      await supabase
        .from('processing_jobs')
        .update({ 
          status: 'processing',
          updated_at: new Date().toISOString()
        })
        .eq('id', job.id);

      // 7. Process image with AI (ALL HAPPENS SERVER-SIDE)
      console.log('Starting AI processing...');
      let outputImageUrl: string | null = null;

      switch (options.operationType) {
        case 'declutter':
          console.log('Processing with declutter agent...');
          outputImageUrl = await this.performDeclutterOperation(imageUrl, options, supabase);
          break;
        case 'staging':
          console.log('Processing with staging agent...');
          outputImageUrl = await this.performStagingOperation(imageUrl, options, supabase);
          break;
        case 'chain':
          console.log('Processing with chain agent...');
          // First declutter, then stage
          const declutteredUrl = await this.performDeclutterOperation(imageUrl, options, supabase);
          if (declutteredUrl) {
            outputImageUrl = await this.performStagingOperation(declutteredUrl, options, supabase);
          }
          break;
      }

      console.log('AI processing completed. Output URL:', outputImageUrl);

      if (outputImageUrl) {
        // 8. Add watermark to processed image
        console.log('Adding watermark...');
        const watermarkedUrl = await this.addWatermarkToImage(outputImageUrl);
        
        // 9. Update job as completed
        console.log('Updating job status to completed...');
        await supabase
          .from('processing_jobs')
          .update({
            status: 'completed',
            output_image_url: watermarkedUrl || outputImageUrl,
            updated_at: new Date().toISOString(),
          })
          .eq('id', job.id);
        
        // 10. Log usage for billing/analytics
        console.log('Logging usage...');
        await logUsage(userId, propertyId, job.id, photoCost, options.operationType);

        console.log('=== AI Processing Completed Successfully ===');
        return {
          success: true,
          jobId: job.id,
          outputImageUrl: watermarkedUrl || outputImageUrl,
          photosConsumed: photoCost
        };
      } else {
        console.error('AI processing failed - no output image URL');
        // 11. Mark job as failed
        await supabase
          .from('processing_jobs')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString(),
          })
          .eq('id', job.id);
        return { success: false, error: 'AI processing failed', photosConsumed: 0 };
      }

    } catch (error) {
      console.error('=== AI Processing Error ===');
      console.error('Error details:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        photosConsumed: 0
      };
    }
  }

  private static getPhotoCost(operationType: string): number {
    const costs = {
      declutter: 1,
      staging: 1,
      chain: 2, // declutter + staging
    };
    return costs[operationType as keyof typeof costs] || 1;
  }

  private static async uploadImageToStorage(
    file: File,
    userId: string,
    propertyId: string,
    supabase: SupabaseClient
  ): Promise<string | null> {
    try {
      // Create unique filename with timestamp
      const fileExt = file.name.split('.').pop() || 'jpg';
      const fileName = `${userId}/${propertyId}/input/${Date.now()}.${fileExt}`;
      
      // Upload to secure storage bucket
      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Upload error:', error);
        return null;
      }

      // Get public URL for internal processing
      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (error) {
      console.error('Image upload error:', error);
      return null;
    }
  }

  private static async performDeclutterOperation(
    imageUrl: string,
    options: ProcessingOptions,
    supabase: SupabaseClient
  ): Promise<string | null> {
    try {
      console.log('Starting declutter operation for:', imageUrl);
      
      // Build declutter prompt
      const prompt = this.buildDeclutterPrompt(options);
      console.log('Declutter prompt:', prompt);
      
      // Convert image to base64 for API call
      const base64Image = await this.convertImageToBase64(imageUrl);
      console.log('Image converted to base64, length:', base64Image.length);
      
      // Call Gemini 2.0 Flash Preview Image Generation API
      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              },
              {
                inline_data: {
                  mime_type: "image/jpeg",
                  data: base64Image
                }
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.4,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
          responseModalities: ["TEXT", "IMAGE"]
        }
      };

      console.log('Making Gemini API call...');
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=${this.GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Gemini API response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gemini API error response:', errorText);
        return null;
      }

      const result = await response.json();
      console.log('Gemini API result structure:', JSON.stringify(result, null, 2));
      
      // Extract generated image from Gemini response
      if (result.candidates && result.candidates.length > 0) {
        const candidate = result.candidates[0];
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.data) {
              console.log('Found generated image in Gemini response');
              
              // Extract userId and propertyId from the original URL
              const urlParts = imageUrl.split('/');
              const storageIndex = urlParts.findIndex(part => part === 'property-images');
              
              if (storageIndex === -1 || storageIndex + 3 >= urlParts.length) {
                throw new Error('Cannot extract user/property info from URL');
              }
              
              const userId = urlParts[storageIndex + 1];
              const propertyId = urlParts[storageIndex + 2];
              
              // Upload the generated image
              return await this.uploadGeneratedImage(
                part.inlineData.data,
                'decluttered',
                userId,
                propertyId,
                supabase
              );
            }
          }
        }
      }
      
      // Fallback: copy original image if no generated image found
      console.log('No generated image found in response, using fallback');
      return await this.copyImageAsProcessed(imageUrl, 'decluttered', supabase);

    } catch (error) {
      console.error('Declutter operation error:', error);
      return null;
    }
  }

  private static async performStagingOperation(
    imageUrl: string,
    options: ProcessingOptions,
    supabase: SupabaseClient
  ): Promise<string | null> {
    try {
      console.log('Starting staging operation for:', imageUrl);
      
      // Build staging prompt
      const prompt = this.buildStagingPrompt(options);
      console.log('Staging prompt:', prompt);
      
      // Convert image to base64 for API call
      const base64Image = await this.convertImageToBase64(imageUrl);
      console.log('Image converted to base64, length:', base64Image.length);
      
      // Call Gemini 2.0 Flash Preview Image Generation API
      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              },
              {
                inline_data: {
                  mime_type: "image/jpeg",
                  data: base64Image
                }
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.6,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
          responseModalities: ["TEXT", "IMAGE"]
        }
      };

      console.log('Making Gemini API call...');
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=${this.GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Gemini API response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gemini API error response:', errorText);
        return null;
      }

      const result = await response.json();
      console.log('Gemini API result structure:', JSON.stringify(result, null, 2));
      
      // Extract generated image from Gemini response
      if (result.candidates && result.candidates.length > 0) {
        const candidate = result.candidates[0];
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.data) {
              console.log('Found generated image in Gemini response');
              
              // Extract userId and propertyId from the original URL
              const urlParts = imageUrl.split('/');
              const storageIndex = urlParts.findIndex(part => part === 'property-images');
              
              if (storageIndex === -1 || storageIndex + 3 >= urlParts.length) {
                throw new Error('Cannot extract user/property info from URL');
              }
              
              const userId = urlParts[storageIndex + 1];
              const propertyId = urlParts[storageIndex + 2];
              
              // Upload the generated image
              return await this.uploadGeneratedImage(
                part.inlineData.data,
                'staged',
                userId,
                propertyId,
                supabase
              );
            }
          }
        }
      }
      
      // Fallback: copy original image if no generated image found
      console.log('No generated image found in response, using fallback');
      return await this.copyImageAsProcessed(imageUrl, 'staged', supabase);

    } catch (error) {
      console.error('Staging operation error:', error);
      return null;
    }
  }

  private static async convertImageToBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl);
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      // Convert buffer to base64 string
      return buffer.toString('base64');
    } catch (error) {
      console.error('Error converting image to base64:', error);
      throw error;
    }
  }

  private static async uploadGeneratedImage(
    base64Data: string, 
    operation: string, 
    userId: string,
    propertyId: string,
    supabase: SupabaseClient
  ): Promise<string> {
    try {
      // Convert base64 to buffer (server-side)
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Create processed filename following user folder structure
      const timestamp = Date.now();
      const fileName = `${userId}/${propertyId}/processed/${operation}/${timestamp}.jpg`;
      
      // Upload processed image to Supabase storage
      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, buffer, {
          cacheControl: '3600',
          upsert: false,
          contentType: 'image/jpeg'
        });

      if (error) {
        console.error('Upload generated image error:', error);
        throw error;
      }

      // Get public URL for the processed image
      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading generated image:', error);
      throw error;
    }
  }

  private static buildDeclutterPrompt(options: ProcessingOptions): string {
    // Get AI agent context from ContextEngine
    const agentContext = ContextEngine.getAgentPromptContext(
      'declutter',
      options.roomType,
      options.designStyle
    );
    
    let prompt = agentContext + "\n\n";
    prompt += "Remove all clutter, personal items, furniture, and unwanted objects from this room while preserving the room's structure, walls, windows, doors, and architectural features. Create a clean, empty space that maintains the original layout and lighting. Keep all permanent fixtures like built-in cabinets, countertops, and flooring.";
    
    if (options.roomType) {
      prompt += ` This is a ${options.roomType.replace('_', ' ')}, so maintain the room's intended function and layout.`;
    }
    
    if (options.customPrompt) {
      prompt += ` Additional instructions: ${options.customPrompt}`;
    }
    
    prompt += " The result should be a clean, empty room ready for staging.";
    
    return prompt;
  }

  private static buildStagingPrompt(options: ProcessingOptions): string {
    // Get AI agent context from ContextEngine
    const agentContext = ContextEngine.getAgentPromptContext(
      'staging',
      options.roomType,
      options.designStyle
    );
    
    let prompt = agentContext + "\n\n";
    prompt += "Add beautiful, realistic furniture and decor to this room to create an appealing, professionally staged space that would attract potential buyers or renters. The furniture should be proportional, modern, and create an inviting atmosphere.";
    
    if (options.roomType) {
      switch (options.roomType.toLowerCase()) {
        case 'living_room':
          prompt += " Add a comfortable sofa, coffee table, side tables, lamps, and tasteful decor. Create a cozy conversation area.";
          break;
        case 'bedroom':
          prompt += " Add a bed with quality bedding, nightstands, dresser, and appropriate lighting. Create a peaceful, restful environment.";
          break;
        case 'kitchen':
          prompt += " Add modern appliances if missing, decorative items on counters, and create a clean, functional cooking space.";
          break;
        case 'dining_room':
          prompt += " Add a dining table with chairs, perhaps a sideboard or buffet, and elegant table settings or decor.";
          break;
        case 'bathroom':
          prompt += " Add towels, toiletries, plants, and decorative elements to create a spa-like, clean environment.";
          break;
        case 'office':
          prompt += " Add a desk, office chair, shelving, and professional decor to create a productive workspace.";
          break;
        default:
          prompt += ` This is a ${options.roomType.replace('_', ' ')}, so add appropriate furniture and decor for this space.`;
      }
    }
    
    if (options.designStyle) {
      prompt += ` Use ${options.designStyle} design style throughout.`;
    }
    
    if (options.customPrompt) {
      prompt += ` Additional requirements: ${options.customPrompt}`;
    }
    
    prompt += " Ensure all furniture is realistic, well-proportioned, and creates an aspirational yet achievable living space.";
    
    return prompt;
  }

  private static async addWatermarkToImage(imageUrl: string): Promise<string | null> {
    try {
      // TODO: Implement actual watermarking using Canvas API or image processing library
      // For now, return the original URL
      // In production, you would add "Staiger AI Virtualization" watermark in bottom-left corner
      
      return imageUrl;
    } catch (error) {
      console.error('Watermark error:', error);
      return imageUrl; // Return original if watermarking fails
    }
  }

  /**
   * Rate limiting to prevent abuse
   */
  static async checkRateLimit(userId: string): Promise<boolean> {
    try {
      // TODO: Implement proper rate limiting using Redis or database
      // For now, return true to allow all requests
      // In production, you would check against Redis or database
      // to enforce limits like 10 requests per minute per user
      console.log(`Rate limit check for user: ${userId}`);
      
      return true;
    } catch (error) {
      console.error('Rate limit check error:', error);
      return false;
    }
  }

  /**
   * Get API usage statistics for monitoring costs
   */
  static async getAPIUsageStats(): Promise<{
    totalRequests: number;
    totalCost: number;
    averageProcessingTime: number;
  }> {
    try {
      // TODO: Implement usage tracking
      // This would track API usage across all users
      // Useful for monitoring costs and performance
      
      return {
        totalRequests: 0,
        totalCost: 0,
        averageProcessingTime: 0,
      };
    } catch (error) {
      console.error('Usage stats error:', error);
      return {
        totalRequests: 0,
        totalCost: 0,
        averageProcessingTime: 0,
      };
    }
  }

  /**
   * Check if image has already been successfully processed (virtualized)
   * Users can reprocess images for additional context cost
   */
  private static async checkSimpleDuplicate(
    userId: string,
    propertyId: string,
    file: File,
    supabase: SupabaseClient
  ): Promise<boolean> {
    try {
      // Create a simple hash based on file size and name
      const simpleHash = `${file.size}_${file.name}_${file.lastModified || Date.now()}`;
      
      // Check if this exact file was already successfully processed
      // We look for completed processing jobs with this image fingerprint
      const { data: existingFingerprint } = await supabase
        .from('image_fingerprints')
        .select(`
          id,
          processing_jobs!inner(
            id,
            status,
            output_image_url
          )
        `)
        .eq('user_id', userId)
        .eq('image_hash', simpleHash)
        .eq('processing_jobs.status', 'completed')
        .not('processing_jobs.output_image_url', 'is', null)
        .single();

      // Only consider it a duplicate if it was successfully virtualized
      return !!existingFingerprint;
    } catch {
      // If error or no match found, allow processing
      return false;
    }
  }

  /**
   * Store image fingerprint linked to processing job
   */
  private static async storeImageFingerprint(
    userId: string,
    propertyId: string,
    file: File,
    jobId: string,
    supabase: SupabaseClient
  ): Promise<void> {
    try {
      const simpleHash = `${file.size}_${file.name}_${file.lastModified || Date.now()}`;
      
      await supabase
        .from('image_fingerprints')
        .insert({
          user_id: userId,
          property_id: propertyId,
          image_hash: simpleHash,
          file_size: file.size,
          dimensions: `${file.size}x${file.type}`, // Store type as dimensions for now
          job_id: jobId, // Link to the processing job
        });
    } catch (error) {
      // Don't fail processing if fingerprint storage fails
      console.error('Failed to store image fingerprint:', error);
    }
  }

  // Helper method to copy original image as processed result (for testing)
  private static async copyImageAsProcessed(
    originalUrl: string,
    operation: string,
    supabase: SupabaseClient
  ): Promise<string> {
    try {
      console.log('Copying original image as processed result...');
      
      // Extract userId and propertyId from the original URL path
      // Original URL format: .../userId/propertyId/input/timestamp.ext
      const urlParts = originalUrl.split('/');
      const storageIndex = urlParts.findIndex(part => part === 'property-images');
      
      if (storageIndex === -1 || storageIndex + 3 >= urlParts.length) {
        throw new Error('Cannot extract user/property info from URL');
      }
      
      const userId = urlParts[storageIndex + 1];
      const propertyId = urlParts[storageIndex + 2];
      
      // Fetch the original image
      const response = await fetch(originalUrl);
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      // Create processed filename following user folder structure
      const timestamp = Date.now();
      const fileName = `${userId}/${propertyId}/processed/${operation}/${timestamp}.jpg`;
      
      // Upload as processed image
      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, buffer, {
          cacheControl: '3600',
          upsert: false,
          contentType: 'image/jpeg'
        });

      if (error) {
        console.error('Upload processed image error:', error);
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(data.path);

      console.log('Processed image uploaded successfully:', publicUrl);
      return publicUrl;
    } catch (error) {
      console.error('Error copying image as processed:', error);
      throw error;
    }
  }
} 